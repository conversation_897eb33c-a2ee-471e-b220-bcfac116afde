import ApiService from '@/services/ApiService'

export type Building = {
    id: number
    name: string
    villageNameAr: string
    cityOrDistrictNameAr: string
    cityOrDistrictNameEn: string
    governorateNameAr: string
    governorateNameEn: string
    countryNameAr: string
    countryNameEn: string
    email?: string
    phone?: string
    description?: string
    status: number
}

export async function getBuildings() {
    return ApiService.get<Building[]>('/Buildings')
}

export async function getBuildingById(id: string) {
    return ApiService.get<Building>(`/Buildings/${id}`)
}

export async function createBuilding(building: Building) {
    return ApiService.post<Building>('/Buildings', building)
}

export async function updateBuilding(id: string, building: Building) {
    return ApiService.put<Building>(`/Buildings/${id}`, building)
}

export async function deleteBuilding(id: string) {
    return ApiService.delete<void>(`/Buildings/${id}`)
}
