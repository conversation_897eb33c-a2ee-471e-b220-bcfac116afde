import ApiService from "./ApiService"
import type { CreatedOrganizationalStructure, OrganizationalStructure } from "@/@types/organizationalStructure"

export async function getOrganizeStructure(): Promise<OrganizationalStructure> {
    return ApiService.get<OrganizationalStructure>('/OrganizationalStructure/tree')
}


export async function getOrganizeStructureByCode(code: string): Promise<OrganizationalStructure> {
    return ApiService.get<OrganizationalStructure>(`/OrganizationalStructure/tree/${code}`)
}


export async function updateOrganizeStructure(code: string, data: CreatedOrganizationalStructure): Promise<OrganizationalStructure> {
    return ApiService.put<OrganizationalStructure>(`/OrganizationalStructure/${code}`, data as unknown as Record<string, unknown>)
}

export async function deleteOrganizeStructure(code: string): Promise<OrganizationalStructure> {
    return ApiService.delete<OrganizationalStructure>(`/OrganizationalStructure/${code}`)
}

export async function createOrganizeStructure(data: CreatedOrganizationalStructure): Promise<OrganizationalStructure> {
    return ApiService.post<OrganizationalStructure>(`/OrganizationalStructure`, data as unknown as Record<string, unknown>)
}




