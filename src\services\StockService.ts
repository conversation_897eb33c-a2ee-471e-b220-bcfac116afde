import ApiService from "./ApiService"
import type { StockColumns, Stock, StockRequest } from "@/@types/stocks"

export async function getStocks() {
    return ApiService.get<StockColumns[]>('/Stocks')
}

export async function getStockById(id: number) {
    return ApiService.get<Stock>(`/Stocks/${id}`)
}

export async function createStock(stock: StockRequest) {
    return ApiService.post<Stock>('/Stocks', stock)
}

export async function updateStock(id: number, stock: StockRequest) {
    return ApiService.put<Stock>(`/Stocks/${id}`, stock)
}

export async function updateStockStatus(id: number, status: number) {
    return ApiService.patch<void>(`/Stocks/${id}/status`, { status })
}

export async function deleteStock(id: number) {
    return ApiService.delete<void>(`/Stocks/${id}`)
}