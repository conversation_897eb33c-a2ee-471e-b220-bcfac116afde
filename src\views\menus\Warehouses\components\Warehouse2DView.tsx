import React, { useState, useRef, useEffect } from 'react';
import { TbSearch, TbBox, TbMapPin, TbEye, TbChevronDown, TbChevronRight } from 'react-icons/tb';
import Input from '@/components/ui/Input';
import Button from '@/components/ui/Button';
import Badge from '@/components/ui/Badge';
import { useWarehouseStore } from '../store/warehouseStore';
import { locate, formatNumber, formatPercentage } from '../utils';
import type { Layout, LocationIndices, BoxStatus } from '../types';

interface Warehouse2DViewProps {
  layout: Layout;
  onBoxSelect?: (boxId: string) => void;
  selectedBoxId?: string | null;
}

interface CollapsedState {
  areas: Set<string>;
  units: Set<string>;
  shelves: Set<string>;
}

const Warehouse2DView: React.FC<Warehouse2DViewProps> = ({
  layout,
  onBoxSelect,
  selectedBoxId,
}) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [highlightedLocation, setHighlightedLocation] = useState<LocationIndices | null>(null);
  const [hoveredBox, setHoveredBox] = useState<string | null>(null);
  const [collapsed, setCollapsed] = useState<CollapsedState>({
    areas: new Set(),
    units: new Set(),
    shelves: new Set(),
  });
  const searchInputRef = useRef<HTMLInputElement>(null);

  // Calculate statistics
  const totalBoxes = layout.areas.reduce((areaAcc, area) => 
    areaAcc + area.units.reduce((unitAcc, unit) => 
      unitAcc + unit.shelves.reduce((shelfAcc, shelf) => 
        shelfAcc + shelf.boxes.length, 0), 0), 0
  );
  
  const filledBoxes = layout.areas.reduce((areaAcc, area) => 
    areaAcc + area.units.reduce((unitAcc, unit) => 
      unitAcc + unit.shelves.reduce((shelfAcc, shelf) => 
        shelfAcc + shelf.boxes.filter(box => box.status === 'filled').length, 0), 0), 0
  );
  
  const emptyBoxes = totalBoxes - filledBoxes;
  const utilizationRate = totalBoxes > 0 ? (filledBoxes / totalBoxes) * 100 : 0;

  // Handle search
  const handleSearch = (query: string) => {
    setSearchQuery(query);
    const location = locate(query);
    setHighlightedLocation(location);
    
    if (location && onBoxSelect) {
      const boxId = `${layout.areas[location.areaIdx]?.units[location.unitIdx]?.shelves[location.shelfIdx]?.boxes[location.boxIdx]?.id}`;
      if (boxId && boxId !== 'undefined') {
        onBoxSelect(boxId);
      }
    }
  };

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Enter' && searchInputRef.current) {
        searchInputRef.current.focus();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, []);

  // Check if a box should be highlighted
  const isBoxHighlighted = (areaIdx: number, unitIdx: number, shelfIdx: number, boxIdx: number) => {
    if (!highlightedLocation) return false;
    return (
      areaIdx === highlightedLocation.areaIdx &&
      unitIdx === highlightedLocation.unitIdx &&
      shelfIdx === highlightedLocation.shelfIdx &&
      boxIdx === highlightedLocation.boxIdx
    );
  };

  // Check if a box is selected
  const isBoxSelected = (boxId: string) => {
    return selectedBoxId === boxId;
  };

  // Get box styling based on status
  const getBoxStyling = (box: any, isHighlighted: boolean, isSelected: boolean, isHovered: boolean) => {
    const baseClasses = "w-8 h-8 rounded text-xs font-medium transition-all duration-200";
    
    if (isSelected) {
      return `${baseClasses} bg-blue-500 text-white shadow-lg scale-110`;
    }
    
    if (isHighlighted) {
      return `${baseClasses} bg-yellow-400 text-yellow-900 shadow-md scale-105`;
    }
    
    if (isHovered) {
      return `${baseClasses} scale-105 ${
        box.status === 'filled' 
          ? 'bg-green-200 dark:bg-green-600 text-green-700 dark:text-green-300' 
          : 'bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-300'
      }`;
    }
    
    // Default styling based on box status
    if (box.status === 'filled') {
      return `${baseClasses} bg-green-100 dark:bg-green-700 text-green-600 dark:text-green-300 hover:bg-green-200 dark:hover:bg-green-600`;
    } else {
      return `${baseClasses} bg-gray-100 dark:bg-gray-700 text-gray-400 dark:text-gray-500 hover:bg-gray-200 dark:hover:bg-gray-600`;
    }
  };

  // Toggle collapse state
  const toggleArea = (areaId: string) => {
    setCollapsed(prev => ({
      ...prev,
      areas: new Set(prev.areas.has(areaId) 
        ? Array.from(prev.areas).filter(id => id !== areaId)
        : [...Array.from(prev.areas), areaId]
      )
    }));
  };

  const toggleUnit = (unitId: string) => {
    setCollapsed(prev => ({
      ...prev,
      units: new Set(prev.units.has(unitId) 
        ? Array.from(prev.units).filter(id => id !== unitId)
        : [...Array.from(prev.units), unitId]
      )
    }));
  };

  const toggleShelf = (shelfId: string) => {
    setCollapsed(prev => ({
      ...prev,
      shelves: new Set(prev.shelves.has(shelfId) 
        ? Array.from(prev.shelves).filter(id => id !== shelfId)
        : [...Array.from(prev.shelves), shelfId]
      )
    }));
  };

  // Expand all or collapse all
  const expandAll = () => {
    setCollapsed({
      areas: new Set(),
      units: new Set(),
      shelves: new Set(),
    });
  };

  const collapseAll = () => {
    const allAreas = new Set(layout.areas.map(area => area.id));
    const allUnits = new Set(layout.areas.flatMap(area => area.units.map(unit => unit.id)));
    const allShelves = new Set(layout.areas.flatMap(area => 
      area.units.flatMap(unit => unit.shelves.map(shelf => shelf.id))
    ));
    
    setCollapsed({
      areas: allAreas,
      units: allUnits,
      shelves: allShelves,
    });
  };

  return (
    <div className="space-y-6">
      {/* Search Bar */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="flex-1 relative">
          <TbSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
          <Input
            ref={searchInputRef}
            placeholder="Search by box ID (e.g., A1-U2-S3-B10)..."
            value={searchQuery}
            onChange={(e) => handleSearch(e.target.value)}
            className="pl-10"
          />
        </div>
        <div className="flex gap-2">
          <Button
            variant="plain"
            onClick={expandAll}
            className="flex items-center gap-2"
          >
            <TbChevronDown size={16} />
            Expand All
          </Button>
          <Button
            variant="plain"
            onClick={collapseAll}
            className="flex items-center gap-2"
          >
            <TbChevronRight size={16} />
            Collapse All
          </Button>
          <Button
            variant="plain"
            onClick={() => {
              setSearchQuery('');
              setHighlightedLocation(null);
            }}
          >
            Clear
          </Button>
          {highlightedLocation && (
            <Badge className="bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300">
              Found: {searchQuery}
            </Badge>
          )}
        </div>
      </div>

      {/* Statistics Bar */}
      <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
          <div>
            <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
              {formatNumber(totalBoxes)}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">Total Boxes</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-green-600 dark:text-green-400">
              {formatNumber(filledBoxes)}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">Filled Boxes</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-gray-600 dark:text-gray-400">
              {formatNumber(emptyBoxes)}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">Empty Slots</div>
          </div>
          <div>
            <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">
              {formatPercentage(utilizationRate)}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">Utilization</div>
          </div>
        </div>
      </div>

      {/* Layout Grid */}
      <div className="space-y-8">
        {layout.areas.map((area, areaIdx) => {
          const isAreaCollapsed = collapsed.areas.has(area.id);
          
          return (
            <div key={area.id} className="border border-gray-200 dark:border-gray-700 rounded-xl p-6 bg-white dark:bg-gray-800">
              {/* Area Header */}
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center gap-3">
                  <button
                    onClick={() => toggleArea(area.id)}
                    className="flex items-center gap-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg p-2 transition-colors"
                  >
                    {isAreaCollapsed ? (
                      <TbChevronRight size={20} className="text-gray-500" />
                    ) : (
                      <TbChevronDown size={20} className="text-gray-500" />
                    )}
                    <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center text-white font-bold">
                      {area.id}
                    </div>
                  </button>
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                      Area {areaIdx + 1}
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {area.units.length} units, {formatNumber(area.units.reduce((acc, unit) => acc + unit.shelves.length, 0))} shelves
                    </p>
                  </div>
                </div>
                <Badge className="bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300">
                  {formatNumber(area.units.reduce((acc, unit) => 
                    acc + unit.shelves.reduce((sacc, shelf) => sacc + shelf.boxes.length, 0), 0
                  ))} boxes
                </Badge>
              </div>

              {/* Units Grid - Only show if area is not collapsed */}
              {!isAreaCollapsed && (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                  {area.units.map((unit, unitIdx) => {
                    const isUnitCollapsed = collapsed.units.has(unit.id);
                    
                    return (
                      <div key={unit.id} className="border border-gray-200 dark:border-gray-600 rounded-lg p-4 bg-gray-50 dark:bg-gray-700">
                        {/* Unit Header */}
                        <div className="flex items-center justify-between mb-4">
                          <div className="flex items-center gap-2">
                            <button
                              onClick={() => toggleUnit(unit.id)}
                              className="flex items-center gap-1 hover:bg-gray-200 dark:hover:bg-gray-600 rounded p-1 transition-colors"
                            >
                              {isUnitCollapsed ? (
                                <TbChevronRight size={16} className="text-gray-500" />
                              ) : (
                                <TbChevronDown size={16} className="text-gray-500" />
                              )}
                              <div className="w-8 h-8 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-md flex items-center justify-center text-white text-sm font-bold">
                                {unit.id.split('-')[1]}
                              </div>
                            </button>
                            <span className="font-medium text-gray-900 dark:text-gray-100">
                              Unit {unitIdx + 1}
                            </span>
                          </div>
                          <Badge className="bg-indigo-100 text-indigo-700 dark:bg-indigo-900 dark:text-indigo-300 text-xs">
                            {unit.shelves.length} shelves
                          </Badge>
                        </div>

                        {/* Shelves Stack - Only show if unit is not collapsed */}
                        {!isUnitCollapsed && (
                          <div className="space-y-3">
                            {unit.shelves.map((shelf, shelfIdx) => {
                              const isShelfCollapsed = collapsed.shelves.has(shelf.id);
                              const filledCount = shelf.boxes.filter(box => box.status === 'filled').length;
                              const emptyCount = shelf.boxes.filter(box => box.status === 'empty').length;
                              
                              return (
                                <div key={shelf.id} className="border border-gray-200 dark:border-gray-600 rounded-md p-3 bg-white dark:bg-gray-800">
                                  {/* Shelf Header */}
                                  <div className="flex items-center justify-between mb-3">
                                    <div className="flex items-center gap-2">
                                      <button
                                        onClick={() => toggleShelf(shelf.id)}
                                        className="flex items-center gap-1 hover:bg-gray-100 dark:hover:bg-gray-700 rounded p-1 transition-colors"
                                      >
                                        {isShelfCollapsed ? (
                                          <TbChevronRight size={14} className="text-gray-500" />
                                        ) : (
                                          <TbChevronDown size={14} className="text-gray-500" />
                                        )}
                                        <TbBox size={16} className="text-gray-500" />
                                      </button>
                                      <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                                        Shelf {shelfIdx + 1}
                                      </span>
                                    </div>
                                    <div className="flex gap-1 text-xs">
                                      <span className="text-green-600 dark:text-green-400">{filledCount} filled</span>
                                      <span className="text-gray-400">/</span>
                                      <span className="text-gray-500">{emptyCount} empty</span>
                                    </div>
                                  </div>

                                  {/* Boxes Grid - Only show if shelf is not collapsed */}
                                  {!isShelfCollapsed && (
                                    <div className="grid grid-cols-4 gap-1">
                                      {shelf.boxes.map((box, boxIdx) => {
                                        const isHighlighted = isBoxHighlighted(areaIdx, unitIdx, shelfIdx, boxIdx);
                                        const isSelected = isBoxSelected(box.id);
                                        const isHovered = hoveredBox === box.id;
                                        
                                        return (
                                          <button
                                            key={box.id}
                                            onClick={() => onBoxSelect?.(box.id)}
                                            onMouseEnter={() => setHoveredBox(box.id)}
                                            onMouseLeave={() => setHoveredBox(null)}
                                            className={getBoxStyling(box, isHighlighted, isSelected, isHovered)}
                                            title={`${box.id} - ${box.status === 'filled' ? 'Filled' : 'Empty'}${box.content ? `: ${box.content}` : ''}`}
                                          >
                                            {boxIdx + 1}
                                          </button>
                                        );
                                      })}
                                    </div>
                                  )}
                                </div>
                              );
                            })}
                          </div>
                        )}
                      </div>
                    );
                  })}
                </div>
              )}
            </div>
          );
        })}
      </div>

      {/* Legend */}
      <div className="bg-gray-50 dark:bg-gray-800 rounded-lg p-4">
        <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-3">Legend</h4>
        <div className="flex flex-wrap gap-4 text-sm">
          <div className="flex items-center gap-2">
            <div className="w-4 h-4 bg-blue-500 rounded"></div>
            <span className="text-gray-600 dark:text-gray-400">Selected Box</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-4 h-4 bg-yellow-400 rounded"></div>
            <span className="text-gray-600 dark:text-gray-400">Search Result</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-4 h-4 bg-green-100 dark:bg-green-700 rounded"></div>
            <span className="text-gray-600 dark:text-gray-400">Filled Box</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-4 h-4 bg-gray-100 dark:bg-gray-700 rounded"></div>
            <span className="text-gray-600 dark:text-gray-400">Empty Slot</span>
          </div>
          <div className="flex items-center gap-2">
            <TbChevronDown size={16} className="text-gray-500" />
            <span className="text-gray-600 dark:text-gray-400">Expand/Collapse</span>
          </div>
        </div>
      </div>

      {/* Selected Box Info */}
      {selectedBoxId && (
        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
          <div className="flex items-center gap-2 mb-2">
            <TbMapPin className="text-blue-600 dark:text-blue-400" size={20} />
            <span className="font-medium text-blue-900 dark:text-blue-100">Selected Box</span>
          </div>
          <div className="text-blue-800 dark:text-blue-200 font-mono text-lg">
            {selectedBoxId}
          </div>
          <p className="text-sm text-blue-700 dark:text-blue-300 mt-1">
            Click on any box to select it and view its location details
          </p>
        </div>
      )}
    </div>
  );
};

export default Warehouse2DView;
