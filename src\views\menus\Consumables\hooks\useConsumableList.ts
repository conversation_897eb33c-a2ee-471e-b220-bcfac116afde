import { consumablesData } from '@/mock/data/consumablesData'
import { useConsumableListStore } from '../store/consumableListStore'
import type { GetConsumableListResponse } from '../types'

const useConsumableList = () => {
    const {
        tableData,
        filterData,
        setTableData,
        setFilterData,
        selectedConsumable,
        setSelectedConsumable,
        setSelectAllConsumable,
    } = useConsumableListStore((state) => state)

    // Use mock data directly and map to correct type
    const mappedData = consumablesData.map(item => ({
        ...item,
        attachments: item.Attachments // Map Attachments to attachments
    }))

    const data: GetConsumableListResponse = {
        list: mappedData,
        total: mappedData.length
    }

    const consumableList = data?.list || []

    const consumableListTotal = data?.total || 0

    // Mock mutate function for compatibility
    const mutate = () => {
        // No-op since we're using static mock data
    }

    return {
        error: null,
        isLoading: false,
        tableData,
        filterData,
        mutate,
        consumableList,
        consumableListTotal,
        setTableData,
        selectedConsumable,
        setSelectedConsumable,
        setSelectAllConsumable,
        setFilterData,
    }
}

export default useConsumableList
