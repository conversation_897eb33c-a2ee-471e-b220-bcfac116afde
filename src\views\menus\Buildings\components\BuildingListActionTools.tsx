import { useState } from 'react'
import Button from '@/components/ui/Button'
import { TbCloudDownload, TbPlus } from 'react-icons/tb'
import useBuildingList from '../hooks/useBuildingList'
import { CSVLink } from 'react-csv'
import BuildingModal from './BuildingModal'
import useTranslation from '@/utils/hooks/useTranslation'

const BuildingListActionTools = () => {
    const [isModalOpen, setIsModalOpen] = useState(false)
    const { buildingList, mutate } = useBuildingList()
    const { t } = useTranslation()

    const handleAddBuilding = () => {
        setIsModalOpen(true)
    }

    const handleModalClose = () => {
        setIsModalOpen(false)
    }

    const handleSuccess = () => {
        // Refresh the list after successful creation/update
        mutate()
    }

    return (
        <>
            <div className="flex flex-col sm:flex-row gap-3">
                {/* todo: handle export */}
                <CSVLink
                    filename="buildings-list.csv"
                    data={buildingList.map((building) => ({
                        Name: building.name,
                        Village: building.villageNameAr,
                        City: building.cityOrDistrictNameAr,
                        Governorate: building.governorateNameAr,
                        Country: building.countryNameAr,
                        Status: building.status === 1 ? 'معتمد' : 'غير معتمد',
                    }))}
                >
                    <Button icon={<TbCloudDownload className="text-xl" />}>
                        {t('nav.shared.export')}
                    </Button>
                </CSVLink>
                <Button
                    variant="solid"
                    icon={<TbPlus className="text-xl" />}
                    onClick={handleAddBuilding}
                >
                    {t('nav.shared.add')}
                </Button>
            </div>

            <BuildingModal
                isOpen={isModalOpen}
                onClose={handleModalClose}
                onSuccess={handleSuccess}
            />
        </>
    )
}

export default BuildingListActionTools
