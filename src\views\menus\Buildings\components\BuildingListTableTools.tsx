import BuildingListSearch from './BuildingListSearch'
import BuildingTableFilter from './BuildingTableFilter'
import useBuildingList from '../hooks/useBuildingList'
import cloneDeep from 'lodash/cloneDeep'

const BuildingListTableTools = () => {
    const { tableData, setTableData, handleSearch } = useBuildingList()

    const handleInputChange = (val: string) => {
        const newTableData = cloneDeep(tableData)
        newTableData.query = val
        newTableData.pageIndex = 1
        setTableData(newTableData)
        handleSearch(val)
    }

    return (
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-2">
            <BuildingListSearch onInputChange={handleInputChange} />
            <BuildingTableFilter />
        </div>
    )
}

export default BuildingListTableTools
