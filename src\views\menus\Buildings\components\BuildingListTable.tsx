import { useMemo, useState } from 'react'
import Tooltip from '@/components/ui/Tooltip'
import DataTable from '@/components/shared/DataTable'
import ConfirmDialog from '@/components/shared/ConfirmDialog'
import useBuildingList from '../hooks/useBuildingList'
import cloneDeep from 'lodash/cloneDeep'
import { TbPencil, TbTrash } from 'react-icons/tb'
import type { OnSortParam, ColumnDef, Row } from '@/components/shared/DataTable'
import type { BuildingWithDisplay } from '../hooks/useBuildingList'
import type { TableQueries } from '@/@types/common'
import { Tag } from '@/components/ui'
import BuildingModal from './BuildingModal'
import useTranslation from '@/utils/hooks/useTranslation'

const ActionColumn = ({
    onEdit,
    onDelete,
}: {
    onEdit: () => void
    onDelete: () => void
}) => {
    const { t } = useTranslation()
    return (
        <div className="flex items-center justify-center gap-3">
            <Tooltip title={t('nav.shared.edit')}>
                <div
                    className={`text-xl cursor-pointer select-none font-semibold`}
                    role="button"
                    onClick={onEdit}
                >
                    <TbPencil />
                </div>
            </Tooltip>
            <Tooltip title={t('nav.shared.delete')}>
                <div
                    className={`text-xl cursor-pointer select-none font-semibold`}
                    role="button"
                    onClick={onDelete}
                >
                    <TbTrash />
                </div>
            </Tooltip>
        </div>
    )
}

const statusColor: Record<string, string> = {
    معتمد: 'bg-emerald-200 dark:bg-emerald-200 text-gray-900 dark:text-gray-900',
    'غير معتمد': 'bg-red-200 dark:bg-red-200 text-gray-900 dark:text-gray-900',
}

const BuildingListTable = () => {
    const { t } = useTranslation()
    const [deleteConfirmationOpen, setDeleteConfirmationOpen] = useState(false)
    const [toDeleteId, setToDeleteId] = useState('')
    const [editModalOpen, setEditModalOpen] = useState(false)
    const [editingBuilding, setEditingBuilding] = useState<
        BuildingWithDisplay | undefined
    >()

    const handleCancel = () => {
        setDeleteConfirmationOpen(false)
    }

    const handleDelete = (building: BuildingWithDisplay) => {
        setDeleteConfirmationOpen(true)
        setToDeleteId(building.id.toString())
    }

    const handleEdit = (building: BuildingWithDisplay) => {
        setEditingBuilding(building)
        setEditModalOpen(true)
    }

    const handleEditModalClose = () => {
        setEditModalOpen(false)
        setEditingBuilding(undefined)
    }

    const handleEditSuccess = () => {
        // Refresh the list after successful update
        mutate()
    }


    const {
        buildingList,
        buildingListTotal,
        tableData,
        // filterData,
        isLoading,
        setTableData,
        setSelectAllBuilding,
        setSelectedBuilding,
        selectedBuilding,
        mutate,
    } = useBuildingList()

    const allColumns: ColumnDef<BuildingWithDisplay>[] = useMemo(
        () => [
            {
                header: t('nav.buildings.name'),
                accessorKey: 'name',
                cell: (props) => {
                    const row = props.row.original
                    return <span className=" heading-text">{row.name}</span>
                },
            },
            {
                header: t('nav.buildings.village'),
                accessorKey: 'displayVillage',
                cell: (props) => {
                    const row = props.row.original
                    return (
                        <span className=" heading-text">
                            {row.displayVillage}
                        </span>
                    )
                },
            },
            {
                header: t('nav.buildings.city'),
                accessorKey: 'displayCity',
                cell: (props) => {
                    const row = props.row.original
                    return (
                        <span className=" heading-text">{row.displayCity}</span>
                    )
                },
            },
            {
                header: t('nav.buildings.governorate'),
                accessorKey: 'displayGovernorate',
                cell: (props) => {
                    const row = props.row.original
                    return (
                        <span className=" heading-text">
                            {row.displayGovernorate}
                        </span>
                    )
                },
            },
            {
                header: t('nav.buildings.country'),
                accessorKey: 'displayCountry',
                cell: (props) => {
                    const row = props.row.original
                    return (
                        <span className=" heading-text">
                            {row.displayCountry}
                        </span>
                    )
                },
            },
            {
                header: t('nav.shared.status'),
                accessorKey: 'status',
                cell: (props) => {
                    const row = props.row.original
                    const statusText = row.status === 1 ? 'معتمد' : 'غير معتمد'
                    return (
                        <div className="flex items-center justify-center">
                            <Tag className={statusColor[statusText]}>
                                <span className="capitalize">{statusText}</span>
                            </Tag>
                        </div>
                    )
                },
            },

            {
                header: t('nav.shared.edit'),
                id: 'action',
                cell: (props) => (
                    <ActionColumn
                        onEdit={() => handleEdit(props.row.original)}
                        onDelete={() => handleDelete(props.row.original)}
                    />
                ),
            },
        ],
        [t],
    )

    const columns: ColumnDef<BuildingWithDisplay>[] = useMemo(() => {
        return allColumns
    }, [allColumns])

    const handleSetTableData = (data: TableQueries) => {
        setTableData(data)
        if (selectedBuilding.length > 0) {
            setSelectAllBuilding([])
        }
    }

    const handlePaginationChange = (page: number) => {
        const newTableData = cloneDeep(tableData)
        newTableData.pageIndex = page
        handleSetTableData(newTableData)
    }

    const handleSelectChange = (value: number) => {
        const newTableData = cloneDeep(tableData)
        newTableData.pageSize = Number(value)
        newTableData.pageIndex = 1
        handleSetTableData(newTableData)
    }

    const handleSort = (sort: OnSortParam) => {
        const newTableData = cloneDeep(tableData)
        newTableData.sort = {
            order: sort.order,
            key: sort.key.toString(),
        }
        handleSetTableData(newTableData)
    }

    const handleRowSelect = (checked: boolean, row: BuildingWithDisplay) => {
        setSelectedBuilding(checked ? [row] : [])
    }

    const handleAllRowSelect = (
        checked: boolean,
        rows: Row<BuildingWithDisplay>[],
    ) => {
        if (checked) {
            const originalRows = rows.map((row) => row.original)
            setSelectAllBuilding(originalRows)
        } else {
            setSelectAllBuilding([])
        }
    }

    return (
        <>
            <DataTable
                selectable
                columns={columns}
                data={buildingList}
                noData={!isLoading && buildingList.length === 0}
                skeletonAvatarColumns={[0]}
                skeletonAvatarProps={{ width: 28, height: 28 }}
                loading={isLoading}
                pagingData={{
                    total: buildingListTotal,
                    pageIndex: tableData.pageIndex as number,
                    pageSize: tableData.pageSize as number,
                }}
                checkboxChecked={(row) =>
                    selectedBuilding.some((selected) => selected.id === row.id)
                }
                cellBorder={true}
                onPaginationChange={handlePaginationChange}
                onSelectChange={handleSelectChange}
                onSort={handleSort}
                onCheckBoxChange={handleRowSelect}
                onIndeterminateCheckBoxChange={handleAllRowSelect}
            />
            <ConfirmDialog
                isOpen={deleteConfirmationOpen}
                type="danger"
                title={t('nav.buildings.removeBuilding')}
                onClose={handleCancel}
                onRequestClose={handleCancel}
                onCancel={handleCancel}
                // onConfirm={handleConfirmDelete}
            >
                <p> {t('nav.buildings.confirmRemoveBuilding')}</p>
            </ConfirmDialog>

            <BuildingModal
                isOpen={editModalOpen}
                editData={editingBuilding}
                onSuccess={handleEditSuccess}
                onClose={handleEditModalClose}
            />
        </>
    )
}

export default BuildingListTable
