import Input from '@/components/ui/Input'
import { Button } from '@/components/ui/Button'
import { TbPlus, TbTrash } from 'react-icons/tb'
import useTranslation from '@/utils/hooks/useTranslation'
import { Droppable, Draggable } from '@hello-pangea/dnd'
import { NewItem, Ticket } from '../types'
// import { Select } from '@/components/ui'

type ScrumBoardHeaderProps = {
    newItems: NewItem[]
    setNewItems: (items: NewItem[]) => void
    onAddItem: () => void
    pendingItems?: Ticket[]
    setPendingItems?: (items: Ticket[]) => void
}

const ScrumBoardHeader = ({ newItems, setNewItems, onAddItem, pendingItems, setPendingItems }: ScrumBoardHeaderProps) => {
    const { t } = useTranslation()

    const handleDeleteItem = (itemId: string) => {
        if (setPendingItems && pendingItems) {
            const updatedItems = pendingItems.filter(item => item.id !== itemId)
            setPendingItems(updatedItems)
        }
    }


    return (
        <div className="flex flex-col justify-between">
        <div className="flex flex-col md:flex-row md:items-center justify-between gap-4 mb-6">
            <div>
                <h3>{t('nav.unitStructure.structure')}</h3>
            </div>
            <div className="flex flex-col lg:flex-row justify-between lg:items-center gap-4">
                <div className="flex flex-col md:flex-row md:items-center gap-4">
                    <div className="flex items-center gap-2">
                        <Input
                            placeholder={t('nav.unitStructure.itemName')}
                            value={newItems[0]?.name}
                            className="w-40"
                            onChange={(e) => setNewItems([{ ...newItems[0], name: e.target.value }])}
                        />
                        <Input
                            placeholder={t('nav.unitStructure.description')}
                            value={newItems[0]?.description}
                            className="w-48"
                            onChange={(e) => setNewItems([{ ...newItems[0], description: e.target.value }])}
                        />
                        {/* <Select
                            options={[]}
                            placeholder={t('nav.unitStructure.selectParent')}
                            onChange={(e) => setNewItem({ ...newItem, parentCode: e.target.value })}
                            className="w-48"
                        /> */}
                        <Button
                            variant="solid"
                            color="blue"
                            icon={<TbPlus />}
                            disabled={!newItems[0]?.name.trim()}
                            size="sm"
                            onClick={onAddItem}
                        >
                            {t('nav.shared.add')}
                        </Button>
                    </div>
                </div>
            </div>
        </div>
        {pendingItems && pendingItems.length > 0 && (
            <Droppable droppableId="new-item-container" type="ITEMS">
                {(provided, snapshot) => (
                    <div
                        ref={provided.innerRef}
                        {...provided.droppableProps}
                        className={`p-2 rounded-lg border-2 transition-colors ${
                            snapshot.isDraggingOver 
                                ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' 
                                : 'border-gray-300 dark:border-gray-600'
                        }`}
                    >
                        {pendingItems.map((item, index) => (
                            <Draggable key={item.id} draggableId={`pending-item-${index}`} index={index}>
                                {(provided, dragSnapshot) => (
                                    <div className="flex items-center justify-between gap-4 mb-2">
                                    <div
                                        ref={provided.innerRef}
                                        {...provided.draggableProps}
                                        {...provided.dragHandleProps}
                                        className={`transition-all duration-200  w-full ${
                                            dragSnapshot.isDragging ? 'opacity-75 scale-105' : ''
                                        }`}
                                    >
                                        <div className="bg-blue-100 dark:bg-blue-800 border border-blue-300 dark:border-blue-600 rounded-lg p-2 shadow-sm hover:shadow-md transition-all cursor-grab active:cursor-grabbing">
                                            <div className="flex items-center gap-2 text-blue-700 dark:text-blue-300">
                                                <TbPlus size={16} />
                                                <span className="text-sm font-medium">
                                                    {item.name}
                                                </span>
                                            </div>
                                            
                                        </div>
                                    </div>
                                    <button className="flex items-center gap-2 text-red-700 dark:text-red-300 hover:text-red-800 dark:hover:text-red-200"
                                    onClick={() => handleDeleteItem(item.id)}>
                                                <TbTrash size={16} />
                                            </button>
                                    </div>
                                )}
                            </Draggable>
                        ))}
                        {provided.placeholder}
                    </div>
                )}
            </Droppable>
        )}
        </div>
    )
}

export default ScrumBoardHeader
