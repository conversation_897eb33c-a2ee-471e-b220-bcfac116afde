import { useEffect, useMemo } from 'react'

import { useOrganizationalStructureStore } from '@/views/menus/UnitStructure/store/organizationalStructureStore'

export type ParentData = {
    code: string
    name: string
    children: ParentData[]
}

export const useFilterData = () => {
    const store = useOrganizationalStructureStore()
    
    const data = useOrganizationalStructureStore(state => state.organizationalStructure)

    useEffect(() => {
        store.fetchOrganizationalStructure()
    }, [store])

    const parentsData: ParentData[] = useMemo(() => {
        if (!data?.Nodes) return []
        
        return data.Nodes.filter(node => node.parentCode === null || node.parentCode === undefined).map(node => ({
            code: node.code,
            name: node.name,
            children: data.Nodes.filter(child => child.parentCode === node.code).map(child => ({ 
                code: child.code,
                name: child.name,
                children: []
            }))
        }))
    }, [data])

    return {
        parentsData
    }
}