import { useState } from 'react'
import Dialog from '@/components/ui/Dialog'
import StockForm from './StockForm'
import useTranslation from '@/utils/hooks/useTranslation'
import { useStock } from '@/utils/hooks/useStock'
import { updateStock } from '@/services/StockService'
import type { StockRequest } from '@/@types/stocks'

interface StockModalProps {
    isOpen: boolean
    onSuccess: () => void
    onClose: () => void
    mode: 'add' | 'edit'
    stock_id?: number
}

const StockModal = ({ isOpen, onSuccess, onClose, mode, stock_id }: StockModalProps) => {
    const { t } = useTranslation()
    const { createStock } = useStock()
    const [isSubmitting, setIsSubmitting] = useState(false)

    const title = mode === 'add' ? t('nav.Stock.addStock') : t('nav.shared.editStock')

    const handleSubmit = async (formData: StockRequest) => {
        setIsSubmitting(true)
        try {
            if (mode === 'add') {
                await createStock(formData)
            } else {
                if (stock_id) {
                    await updateStock(stock_id, formData)
                } else {
                    throw new Error('Stock ID is required for editing')
                }
            }
            onSuccess()
        } catch (error) {
            console.error(`Failed to ${mode} stock:`, error)
        } finally {
            setIsSubmitting(false)
        }
    }

    const handleClose = () => {
        if (!isSubmitting) {
            onClose()
        }
    }



    return (
        <Dialog
            isOpen={isOpen}
            shouldCloseOnEsc={true}
            shouldCloseOnOverlayClick={true}
            width={1200}
            height="90vh"
            className="max-h-[90vh] max-w-[90vw]"
            onClose={handleClose}
            onRequestClose={handleClose}
        >
            <div className="flex flex-col h-full max-h-[90vh]">
                <div className="flex items-center justify-center pb-4 border-b border-gray-200">
                    <h3 className="text-lg font-semibold">{title}</h3>
                </div>

                <div className="flex-1 overflow-y-auto min-h-0">
                    <StockForm
                        isSubmitting={isSubmitting}
                        mode={mode}
                        stock_id={stock_id}
                        onCancel={handleClose}
                        onSubmit={handleSubmit}
                    />
                </div>
            </div>
        </Dialog>
    )
}

export default StockModal
