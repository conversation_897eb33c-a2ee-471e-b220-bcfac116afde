import { useState } from 'react'
import { Dialog } from '@/components/ui/Dialog'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { Alert } from '@/components/ui/Alert'
import { toast } from '@/components/ui/toast'
import { TbEdit, TbTrash } from 'react-icons/tb'
import type { Ticket } from '../types'
import useTranslation from '@/utils/hooks/useTranslation'
import { updateOrganizeStructure } from '@/services/OrganizeStructureService'
import { CreatedOrganizationalStructure } from '@/@types/organizationalStructure'

interface NodeDialogProps {
    isOpen: boolean
    onClose: () => void
    node: Ticket | null
    onDelete: (nodeId: string) => Promise<void>
    onRefresh: () => Promise<void>
}

const NodeDialog = ({ isOpen, onClose, node, onDelete, onRefresh }: NodeDialogProps) => {
    const { t } = useTranslation()
    const [isEditing, setIsEditing] = useState(false)
    const [isDeleting, setIsDeleting] = useState(false)
    const [editData, setEditData] = useState({ name: '', description: '' })
    const [deleteConfirmId, setDeleteConfirmId] = useState('')
    const [isLoading, setIsLoading] = useState(false)

    // Reset state when dialog opens/closes
    const handleClose = () => {
        setIsEditing(false)
        setIsDeleting(false)
        setEditData({ name: '', description: '' })
        setDeleteConfirmId('')
        setIsLoading(false)
        onClose()
    }

    // Initialize edit data when node changes
    const handleEditClick = () => {
        if (node) {
            setEditData({ name: node.name || '', description: node.description || '' })
            setIsEditing(true)
            setIsDeleting(false)
        }
    }

    const handleDeleteClick = () => {
        setIsDeleting(true)
        setIsEditing(false)
        setDeleteConfirmId('')
    }

    const handleSaveEdit = async () => {
        if (!node) return
        
        setIsLoading(true)
        try {
            await updateOrganizeStructure(node.id, {
                name: editData.name,
                description: editData.description,
                parentCode: node.parentId || ''
            } as CreatedOrganizationalStructure)
            toast.push(t('nav.unitStructure.nodeUpdatedSuccessfully'), { placement: 'top-center' })
            await onRefresh() // Refresh the data after successful update
            handleClose()
        } catch (error) {
            console.error(error)
            toast.push(t('nav.unitStructure.failedToUpdateNode'), { placement: 'top-center' })
        } finally {
            setIsLoading(false)
        }
    }

    const handleConfirmDelete = async () => {
        if (!node || deleteConfirmId !== node.id) return
        
        setIsLoading(true)
        try {
            await onDelete(node.id)
            toast.push(t('nav.unitStructure.nodeDeletedSuccessfully'), { placement: 'top-center' })
            await onRefresh() // Refresh the data after successful delete
            handleClose()
        } catch (error) {
            console.error(error)
            toast.push(t('nav.unitStructure.failedToDeleteNode'), { placement: 'top-center' })
        } finally {
            setIsLoading(false)
        }
    }

    if (!node) return null

    return (
        <Dialog isOpen={isOpen} onClose={handleClose}>
            <div className="p-6">
                <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                        {isEditing ? t('nav.unitStructure.editNode') : isDeleting ? t('nav.unitStructure.deleteNode') : t('nav.unitStructure.nodeDetails')}
                    </h3>
                </div>

                {!isEditing && !isDeleting && (
                    <div className="space-y-4">
                        {/* Node Details */}
                        <div className="space-y-3">
                            <div>
                                <label className="text-sm font-medium text-gray-700 dark:text-gray-300">{t('nav.unitStructure.name')}</label>
                                <p className="text-gray-900 dark:text-gray-100">{node.name}</p>
                            </div>
                            <div>
                                <label className="text-sm font-medium text-gray-700 dark:text-gray-300">{t('nav.unitStructure.id')}</label>
                                <p className="text-gray-900 dark:text-gray-100">{node.id}</p>
                            </div>
                            <div>
                                <label className="text-sm font-medium text-gray-700 dark:text-gray-300">{t('nav.unitStructure.description')}</label>
                                <p className="text-gray-900 dark:text-gray-100">{node.description || 'No description'}</p>
                            </div>
                            <div>
                                <label className="text-sm font-medium text-gray-700 dark:text-gray-300">{t('nav.unitStructure.level')}</label>
                                <p className="text-gray-900 dark:text-gray-100">{node.level}</p>
                            </div>
                            <div>
                                <label className="text-sm font-medium text-gray-700 dark:text-gray-300">{t('nav.unitStructure.parentId')}</label>
                                <p className="text-gray-900 dark:text-gray-100">{node.parentId || 'Root level'}</p>
                            </div>
                            {node.childrenCount !== undefined && (
                                <div>
                                    <label className="text-sm font-medium text-gray-700 dark:text-gray-300">{t('nav.unitStructure.childrenCount')}</label>
                                    <p className="text-gray-900 dark:text-gray-100">{node.childrenCount}</p>
                                </div>
                            )}
                        </div>

                        {/* Action Buttons */}
                        <div className="flex gap-3 pt-4">
                            <Button
                                variant="solid"
                                color="blue"
                                icon={<TbEdit />}
                                onClick={handleEditClick}
                                className="flex-1"
                            >
                                {t('nav.unitStructure.editNode')}
                            </Button>
                            <Button
                                variant="solid"
                                color="red"
                                icon={<TbTrash />}
                                onClick={handleDeleteClick}
                                className="flex-1"
                            >
                                {t('nav.unitStructure.deleteNode')}
                            </Button>
                        </div>
                    </div>
                )}

                {isEditing && (
                    <div className="space-y-4">
                        <div>
                            <label className="text-sm font-medium text-gray-700 dark:text-gray-300">{t('nav.unitStructure.name')}</label>
                            <Input
                                value={editData.name}
                                onChange={(e) => setEditData({ ...editData, name: e.target.value })}
                                placeholder={t('nav.unitStructure.enterName')}
                                className="mt-1"
                            />
                        </div>
                        <div>
                            <label className="text-sm font-medium text-gray-700 dark:text-gray-300">{t('nav.unitStructure.description')}</label>
                            <Input
                                value={editData.description}
                                onChange={(e) => setEditData({ ...editData, description: e.target.value })}
                                placeholder={t('nav.unitStructure.enterDescription')}
                                className="mt-1"
                            />
                        </div>
                        <div className="flex gap-3 pt-4">
                            <Button
                                variant="solid"
                                color="blue"
                                onClick={handleSaveEdit}
                                loading={isLoading}
                                className="flex-1"
                            >
                                {t('nav.unitStructure.saveChanges')}
                            </Button>
                            <Button
                                variant="default"
                                onClick={() => setIsEditing(false)}
                                className="flex-1"
                            >
                                {t('nav.unitStructure.cancel')}
                            </Button>
                        </div>
                    </div>
                )}

                {isDeleting && (
                    <div className="space-y-4">
                        <Alert showIcon type="warning">
                            <p>{t('nav.unitStructure.areYouSureYouWantToDeleteThisNode')}</p>
                            <p className="text-sm mt-1">
                                <strong>{t('nav.unitStructure.name')}:</strong> {node.name}<br />
                                <strong>{t('nav.unitStructure.id')}:</strong> {node.id}
                            </p>
                        </Alert>
                        
                        <div>
                            <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                                {t('nav.unitStructure.enterTheNodeIdToConfirmDeletion')}
                            </label>
                            <Input
                                value={deleteConfirmId}
                                onChange={(e) => setDeleteConfirmId(e.target.value)}
                                placeholder={`Enter: ${node.id}`}
                                className="mt-1"
                            />
                        </div>

                        <div className="flex gap-3 pt-4">
                            <Button
                                variant="solid"
                                color="red"
                                onClick={handleConfirmDelete}
                                loading={isLoading}
                                disabled={deleteConfirmId !== node.id}
                                className="flex-1"
                            >
                                {t('nav.unitStructure.confirmDelete')}
                            </Button>
                            <Button
                                variant="default"
                                onClick={() => setIsDeleting(false)}
                                className="flex-1"
                            >
                                {t('nav.unitStructure.cancel')}
                            </Button>
                        </div>
                    </div>
                )}
            </div>
        </Dialog>
    )
}

export default NodeDialog
