import React from 'react';
import Card from '@/components/ui/Card';
import Button from '@/components/ui/Button';
import { TbArrowLeft, TbSettings, TbBox, TbCalendar, TbEdit, TbLayoutGrid } from 'react-icons/tb';
import { useWarehouseStore } from '../store/warehouseStore';
import { capacity, formatNumber, formatDateTime, generateLayout, formatPercentage } from '../utils';
import Warehouse2DView from './Warehouse2DView';
import type { ViewMode } from '../types';
import type { Warehouse } from '../types';

interface WarehouseDetailProps {
  warehouse: Warehouse;
  onClose: () => void;
}

const WarehouseDetail: React.FC<WarehouseDetailProps> = ({ warehouse, onClose }) => {
  const {
    viewMode,
    selectedBoxId,
    isLoading,
    error,
    setViewMode,
    selectBox,
  } = useWarehouseStore();

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/3 mb-4"></div>
          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
        </div>
        <Card className="p-6">
          <div className="animate-pulse space-y-4">
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
          </div>
        </Card>
      </div>
    );
  }

  if (error || !warehouse) {
    return (
      <div className="text-center py-12">
        <div className="text-red-500 mb-4">
          {error || 'Warehouse not found'}
        </div>
        <Button onClick={onClose}>
          Back to Warehouses
        </Button>
      </div>
    );
  }

  const cap = capacity(warehouse);
  const layout = warehouse ? generateLayout(warehouse) : null;

  const handleBoxSelect = (boxId: string) => {
    selectBox(boxId);
  };

  const handleViewModeChange = (mode: ViewMode) => {
    setViewMode(mode);
  };

  const tabs = [
    { id: '2D' as ViewMode, label: '2D View', icon: TbLayoutGrid },
    { id: 'SETTINGS' as ViewMode, label: 'Settings', icon: TbSettings },
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div className="flex items-center gap-4">
          <Button
            variant="plain"
            onClick={onClose}
            className="flex items-center gap-2"
          >
            <TbArrowLeft size={20} />
            Back to Warehouses
          </Button>
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
              {warehouse.name}
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              {warehouse.description || 'Warehouse management and layout'}
            </p>
          </div>
        </div>
        <div className="flex gap-2">
          <Button
            variant="plain"
            onClick={() => {/* TODO: Implement edit functionality */}}
            className="flex items-center gap-2"
          >
            <TbEdit size={16} />
            Edit
          </Button>
        </div>
      </div>

      {/* Warehouse Info Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center text-white">
              <TbBox size={20} />
            </div>
            <div>
              <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {formatNumber(cap.totalAreas)}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Areas</div>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-lg flex items-center justify-center text-white">
              <TbBox size={20} />
            </div>
            <div>
              <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {formatNumber(cap.totalUnits)}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Units</div>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-pink-600 rounded-lg flex items-center justify-center text-white">
              <TbBox size={20} />
            </div>
            <div>
              <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {formatNumber(cap.totalShelves)}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Shelves</div>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-gradient-to-br from-green-500 to-emerald-600 rounded-lg flex items-center justify-center text-white">
              <TbBox size={20} />
            </div>
            <div>
              <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {formatNumber(cap.totalBoxes)}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Total Boxes</div>
            </div>
          </div>
        </Card>
      </div>

      {/* Utilization Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-gradient-to-br from-green-500 to-emerald-600 rounded-lg flex items-center justify-center text-white">
              <TbBox size={20} />
            </div>
            <div>
              <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {formatNumber(cap.filledBoxes)}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Filled Boxes</div>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-gradient-to-br from-gray-500 to-gray-600 rounded-lg flex items-center justify-center text-white">
              <TbBox size={20} />
            </div>
            <div>
              <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {formatNumber(cap.emptyBoxes)}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Empty Slots</div>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center gap-3">
            <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-pink-600 rounded-lg flex items-center justify-center text-white">
              <TbBox size={20} />
            </div>
            <div>
              <div className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                {formatPercentage(cap.utilizationRate)}
              </div>
              <div className="text-sm text-gray-600 dark:text-gray-400">Utilization</div>
            </div>
          </div>
        </Card>
      </div>

      {/* Location and Contact Information */}
      {(warehouse.city || warehouse.governorate || warehouse.address || warehouse.phones?.length || warehouse.emails?.length) && (
        <Card className="p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
            Location & Contact Information
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Location */}
            {(warehouse.city || warehouse.governorate || warehouse.address) && (
              <div>
                <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-3">Location</h4>
                <div className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                  {warehouse.city && warehouse.governorate && (
                    <div>
                      <span className="font-medium">City:</span> {warehouse.city}, {warehouse.governorate}
                    </div>
                  )}
                  {warehouse.address && (
                    <div>
                      <span className="font-medium">Address:</span> {warehouse.address}
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Contact */}
            {(warehouse.phones?.length || warehouse.emails?.length) && (
              <div>
                <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-3">Contact</h4>
                <div className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                  {warehouse.phones && warehouse.phones.length > 0 && (
                    <div>
                      <span className="font-medium">Phone:</span>
                      <div className="ml-2">
                        {warehouse.phones.map((phone, index) => (
                          <div key={index}>{phone}</div>
                        ))}
                      </div>
                    </div>
                  )}
                  {warehouse.emails && warehouse.emails.length > 0 && (
                    <div>
                      <span className="font-medium">Email:</span>
                      <div className="ml-2">
                        {warehouse.emails.map((email, index) => (
                          <div key={index}>{email}</div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </Card>
      )}

      {/* Structure Info */}
      <Card className="p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
          Storage Structure
        </h3>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <div className="text-lg font-bold text-blue-600 dark:text-blue-400">
              {warehouse.areasCount}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">Areas</div>
          </div>
          <div className="text-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <div className="text-lg font-bold text-indigo-600 dark:text-indigo-400">
              {warehouse.unitsPerArea}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">Units per Area</div>
          </div>
          <div className="text-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <div className="text-lg font-bold text-purple-600 dark:text-purple-400">
              {warehouse.shelvesPerUnit}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">Shelves per Unit</div>
          </div>
          <div className="text-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <div className="text-lg font-bold text-green-600 dark:text-green-400">
              {warehouse.boxesPerShelf}
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400">Boxes per Shelf</div>
          </div>
        </div>
        <div className="mt-4 flex items-center justify-between text-sm text-gray-600 dark:text-gray-400">
          <div className="flex items-center gap-1">
            <TbCalendar size={16} />
            <span>Created {formatDateTime(warehouse.createdAt)}</span>
          </div>
          <div className="flex items-center gap-1">
            <TbCalendar size={16} />
            <span>Updated {formatDateTime(warehouse.updatedAt)}</span>
          </div>
        </div>
      </Card>

      {/* Tabs */}
      <Card className="p-0">
        <div className="border-b border-gray-200 dark:border-gray-700">
          <nav className="flex space-x-8 px-6">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => handleViewModeChange(tab.id)}
                  className={`
                    flex items-center gap-2 py-4 px-1 border-b-2 font-medium text-sm transition-colors
                    ${viewMode === tab.id
                      ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                      : 'border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'
                    }
                  `}
                >
                  <Icon size={16} />
                  {tab.label}
                </button>
              );
            })}
          </nav>
        </div>

        <div className="p-6">
          {viewMode === '2D' && layout && (
            <Warehouse2DView
              layout={layout}
              onBoxSelect={handleBoxSelect}
              selectedBoxId={selectedBoxId}
            />
          )}

          {viewMode === 'SETTINGS' && (
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
                  Warehouse Settings
                </h3>
                <p className="text-gray-600 dark:text-gray-400">
                  Edit warehouse configuration and manage settings.
                </p>
              </div>
              
              <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
                <h4 className="font-medium text-yellow-800 dark:text-yellow-200 mb-2">
                  Settings Panel
                </h4>
                <p className="text-sm text-yellow-700 dark:text-yellow-300">
                  This section would contain warehouse configuration options, 
                  user permissions, and system settings. Implementation depends 
                  on your specific requirements.
                </p>
              </div>
            </div>
          )}
        </div>
      </Card>
    </div>
  );
};

export default WarehouseDetail;
