import { useStock } from '@/utils/hooks/useStock'
import Select from '@/components/ui/Select'
import Button from '@/components/ui/Button'
import useTranslation from '@/utils/hooks/useTranslation'
import { TbX } from 'react-icons/tb'

const StockTableFilter = () => {
    const { t } = useTranslation()
    const {
        filters,
        handleFilter,
        clearFilters,
        uniqueGovernorates,
        uniqueCities,
        uniqueManagers,
    } = useStock()

    const hasActiveFilters = Object.values(filters).some(value => value)

    return (
        <div className="flex items-center gap-2">
            <div className="flex items-center gap-2">
                <Select
                    placeholder={t('nav.shared.status')}
                    value={filters.status || ''}
                    className="w-32"
                    onChange={(value) => handleFilter({ status: value as string })}
                >
                    <option value="">{t('nav.shared.all')}</option>
                    <option value="1">{t('nav.shared.active')}</option>
                    <option value="0">{t('nav.shared.inactive')}</option>
                </Select>

                <Select
                    placeholder={t('nav.buildings.governorate')}
                    value={filters.governorate || ''}
                    className="w-40"
                    onChange={(value) => handleFilter({ governorate: value as string })}
                >
                    <option value="">{t('nav.shared.all')}</option>
                    {uniqueGovernorates.map(gov => (
                        <option key={gov} value={gov}>{gov}</option>
                    ))}
                </Select>

                <Select
                    placeholder={t('nav.buildings.city')}
                    value={filters.city || ''}
                    className="w-40"
                    onChange={(value) => handleFilter({ city: value as string })}
                >
                    <option value="">{t('nav.shared.all')}</option>
                    {uniqueCities.map(city => (
                        <option key={city} value={city}>{city}</option>
                    ))}
                </Select>

                <Select
                    placeholder={t('nav.shared.manager')}
                    value={filters.manager || ''}
                    className="w-40"
                    onChange={(value) => handleFilter({ manager: value as string })}
                >
                    <option value="">{t('nav.shared.all')}</option>
                    {uniqueManagers.map(manager => (
                        <option key={manager} value={manager}>{manager}</option>
                    ))}
                </Select>
            </div>

            {hasActiveFilters && (
                <Button
                    size="sm"
                    variant="plain"
                    icon={<TbX />}
                    onClick={clearFilters}
                >
                    {t('nav.shared.clearFilters')}
                </Button>
            )}
        </div>
    )
}

export default StockTableFilter
