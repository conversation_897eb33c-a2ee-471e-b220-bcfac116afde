import { useState } from 'react'
import Dialog from '@/components/ui/Dialog'
import Button from '@/components/ui/Button'
import Notification from '@/components/ui/Notification'
import toast from '@/components/ui/toast'
import ConsumableForm from './ConsumableForm'
import sleep from '@/utils/sleep'
import type { Consumables } from '../types'
import useTranslation from '@/utils/hooks/useTranslation'

type ConsumableModalProps = {
    isOpen: boolean
    onClose: () => void
    editData?: Consumables
    onSuccess?: () => void
}

const ConsumableModal = ({
    isOpen,
    onClose,
    editData,
    onSuccess,
}: ConsumableModalProps) => {
    const { t } = useTranslation()
    const [isSubmitting, setIsSubmitting] = useState(false)

    const isEdit = !!editData

    const handleFormSubmit = async (values: Consumables) => {
        console.log('Submitted values', values)
        setIsSubmitting(true)
        await sleep(800)
        setIsSubmitting(false)

        toast.push(
            <Notification type="success">
                {isEdit ? 'Consumable updated!' : 'Consumable created!'}
            </Notification>,
            { placement: 'top-center' },
        )

        onSuccess?.()
        onClose()
    }

    const getDefaultValues = (): Consumables => {
        if (isEdit && editData) {
            return {
                id: editData.id,
                category: editData.category || '',
                count: editData.count || 0,
                description: editData.description || '',
                wareHouse: editData.wareHouse || '',
                status: editData.status || '',
                attachments: editData.attachments || '',
                assignedBy: editData.assignedBy,
                createdAt: editData.createdAt,
            }
        }

        return {
            category: '',
            count: 0,
            description: '',
            wareHouse: '',
            status: '',
            attachments: '',
        }
    }

    return (
        <>
            <Dialog
                isOpen={isOpen}
                shouldCloseOnOverlayClick={true}
                shouldCloseOnEsc={true}
                width={1000}
                height={600}
                className={'h-min'}
                onClose={onClose}
                onRequestClose={onClose}
            >
                <div className="flex flex-col h-full">
                    <div className="flex items-center justify-center pb-4">
                        <h3 className="text-lg font-semibold">
                            {isEdit ? t('nav.shared.edit') + ' ' + t('nav.consumables.consumables') : t('nav.shared.add') + ' ' + t('nav.consumables.consumables')}
                        </h3>
                    </div>

                    <div className="flex-1 overflow-auto ">
                        <ConsumableForm
                            newConsumable={!isEdit}
                            defaultValues={getDefaultValues()}
                            onFormSubmit={handleFormSubmit}
                        >
                            <div className="flex items-center justify-between px-4 pt-4  bg-white dark:bg-gray-800">
                                <div>
                                    {isEdit && `${t('nav.shared.createdAt')} : ${editData?.createdAt}`} 
                                </div>
                                <Button
                                    variant="solid"
                                    type="submit"
                                    loading={isSubmitting}
                                >
                                    {isEdit ? t('nav.shared.update') : t('nav.shared.create')}
                                </Button>
                            </div>
                        </ConsumableForm>
                    </div>
                </div>
            </Dialog>
        </>
    )
}

export default ConsumableModal
