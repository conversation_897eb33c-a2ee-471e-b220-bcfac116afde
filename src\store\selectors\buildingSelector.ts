import { RootState } from "../store";
import { createSelector } from "@reduxjs/toolkit";
import { useLocaleStore } from "../localeStore";

// basic selectors
export const selectBuildingState = (state: RootState) => state.building

export const selectBuildings = (state: RootState) => selectBuildingState(state).buildings
export const selectBuilding = (state: RootState) => selectBuildingState(state).building
export const selectLoading = (state: RootState) => selectBuildingState(state).loading
export const selectError = (state: RootState) => selectBuildingState(state).error

// Language-aware selectors
export const selectBuildingsWithLanguage = createSelector(
    [selectBuildings],
    (buildings) => {
        const { currentLang } = useLocaleStore.getState()
        const isArabic = currentLang === 'ar'
        
        return buildings.map(building => ({
            ...building,
            // Language-specific fields (village only has Arabic, others have both)
            villageName: building.villageNameAr,
            cityName: isArabic ? building.cityOrDistrictNameAr : building.cityOrDistrictNameEn || building.cityOrDistrictNameAr,
            governorateName: isArabic ? building.governorateNameAr : building.governorateNameEn || building.governorateNameAr,
            countryName: isArabic ? building.countryNameAr : building.countryNameEn || building.countryNameAr,
            // Display fields for UI
            displayName: building.name,
            displayVillage: building.villageNameAr,
            displayCity: isArabic ? building.cityOrDistrictNameAr : building.cityOrDistrictNameEn || building.cityOrDistrictNameAr,
            displayGovernorate: isArabic ? building.governorateNameAr : building.governorateNameEn || building.governorateNameAr,
            displayCountry: isArabic ? building.countryNameAr : building.countryNameEn || building.countryNameAr,
        }))
    }
)

// Language-aware selector for single building
export const selectBuildingWithLanguage = createSelector(
    [selectBuilding],
    (building) => {
        if (!building) return null
        
        const { currentLang } = useLocaleStore.getState()
        const isArabic = currentLang === 'ar'
        
        return {
            ...building,
            villageName: building.villageNameAr,
            cityName: isArabic ? building.cityOrDistrictNameAr : building.cityOrDistrictNameEn || building.cityOrDistrictNameAr,
            governorateName: isArabic ? building.governorateNameAr : building.governorateNameEn || building.governorateNameAr,
            countryName: isArabic ? building.countryNameAr : building.countryNameEn || building.countryNameAr,
            displayName: building.name,
            displayVillage: building.villageNameAr,
            displayCity: isArabic ? building.cityOrDistrictNameAr : building.cityOrDistrictNameEn || building.cityOrDistrictNameAr,
            displayGovernorate: isArabic ? building.governorateNameAr : building.governorateNameEn || building.governorateNameAr,
            displayCountry: isArabic ? building.countryNameAr : building.countryNameEn || building.countryNameAr,
        }
    }
)

// derived selectors
export const selectBuildingsCount = createSelector(
    [selectBuildings],
    (buildings) => buildings.length
)

export const selectActiveBuildings = createSelector(
    [selectBuildings],
    (buildings) => buildings.filter(building => building.status === 1)
)

export const selectInactiveBuildings = createSelector(
    [selectBuildings],
    (buildings) => buildings.filter(building => building.status === 0)
)

export const selectBuildingsByGovernorate = createSelector(
    [selectBuildings, (state: RootState, governorate: string) => governorate],
    (buildings, governorate) => buildings.filter(building => building.governorateNameAr === governorate)
)

export const selectBuildingsByCity = createSelector(
    [selectBuildings, (state: RootState, city: string) => city],
    (buildings, city) => buildings.filter(building => building.cityOrDistrictNameAr === city)
)

export const selectBuildingsByStatus = createSelector(
    [selectBuildings, (state: RootState, status: number) => status],
    (buildings, status) => buildings.filter(building => building.status === status)
)

export const selectUniqueGovernorates = createSelector(
    [selectBuildings],
    (buildings) => [...new Set(buildings.map(building => building.governorateNameAr))].filter(Boolean)
)

export const selectUniqueCities = createSelector(
    [selectBuildings],
    (buildings) => [...new Set(buildings.map(building => building.cityOrDistrictNameAr))].filter(Boolean)
)

export const selectUniqueVillages = createSelector(
    [selectBuildings],
    (buildings) => [...new Set(buildings.map(building => building.villageNameAr))].filter(Boolean)
)

export const selectBuildingsSearch = createSelector(
    [selectBuildings, (state: RootState, searchTerm: string) => searchTerm],
    (buildings, searchTerm) => {
        if (!searchTerm) return buildings
        
        const term = searchTerm.toLowerCase()
        return buildings.filter(building =>
            building.name.toLowerCase().includes(term) ||
            building.villageNameAr.toLowerCase().includes(term) ||
            building.cityOrDistrictNameAr.toLowerCase().includes(term) ||
            building.governorateNameAr.toLowerCase().includes(term)
        )
    }
)

export const selectBuildingsWithFilters = createSelector(
    [
        selectBuildings,
        (state: RootState, filters: { status?: string; governorate?: string; city?: string }) => filters
    ],
    (buildings, filters) => {
        let filtered = buildings

        if (filters.status) {
            filtered = filtered.filter(building => building.status.toString() === filters.status)
        }

        if (filters.governorate) {
            filtered = filtered.filter(building => building.governorateNameAr === filters.governorate)
        }

        if (filters.city) {
            filtered = filtered.filter(building => building.cityOrDistrictNameAr === filters.city)
        }

        return filtered
    }
)

export const selectBuildingById = createSelector(
    [selectBuildings, (state: RootState, id: number) => id],
    (buildings, id) => buildings.find(building => building.id === id)
)

export const selectBuildingsStats = createSelector(
    [selectBuildings],
    (buildings) => {
        const total = buildings.length
        const active = buildings.filter(building => building.status === 1).length
        const inactive = buildings.filter(building => building.status === 0).length
        
        return {
            total,
            active,
            inactive,
            activePercentage: total > 0 ? Math.round((active / total) * 100) : 0,
            inactivePercentage: total > 0 ? Math.round((inactive / total) * 100) : 0
        }
    }
)




