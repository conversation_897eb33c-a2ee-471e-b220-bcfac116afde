import Container from '@/components/shared/Container'
import AdaptiveCard from '@/components/shared/AdaptiveCard'
import ConsumableListActionTools from './components/ConsumableListActionTools'
import ConsumableListTableTools from './components/ConsumableListTableTools'
import ConsumableListTable from './components/ConsumableListTable'
import ConsumableListSelected from './components/ConsumableListSelected'
import useTranslation from '@/utils/hooks/useTranslation'

const ConsumableList = () => {
    const { t } = useTranslation()
    
    return (
        <>
            <Container>
                <AdaptiveCard>
                    <div className="flex flex-col gap-4">
                        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-2">
                            <h3>{t('nav.consumables.consumables')}</h3>
                            <ConsumableListActionTools />
                        </div>
                        <ConsumableListTableTools />
                        <ConsumableListTable />
                    </div>
                </AdaptiveCard>
            </Container>
            <ConsumableListSelected />
        </>
    )
}

export default ConsumableList
