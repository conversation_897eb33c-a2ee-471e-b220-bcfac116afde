import API from './axios/AxiosBase'
import type { AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios'

// Enhanced API Service with better error handling and logging
const ApiService = {
    /**
     * Fetch data with axios - enhanced version with better error handling
     */
    fetchDataWithAxios<Response = unknown, Request = Record<string, unknown>>(
        param: AxiosRequestConfig<Request>,
    ): Promise<Response> {
        return new Promise<Response>((resolve, reject) => {
            // Log request for debugging
            console.log('API Request:', {
                method: param.method?.toUpperCase(),
                url: param.url,
                data: param.data,
                headers: param.headers
            })

            API(param)
                .then((response: AxiosResponse<Response>) => {
                    // Log successful response
                    console.log('API Response:', {
                        status: response.status,
                        statusText: response.statusText,
                        data: response.data
                    })
                    resolve(response.data)
                })
                .catch((error: AxiosError) => {
                    // Enhanced error logging
                    console.error('API Error:', {
                        message: error.message,
                        status: error.response?.status,
                        statusText: error.response?.statusText,
                        data: error.response?.data,
                        config: {
                            method: error.config?.method?.toUpperCase(),
                            url: error.config?.url,
                            headers: error.config?.headers
                        }
                    })
                    reject(error)
                })
        })
    },

    /**
     * GET request helper
     */
    get<Response = unknown>(url: string, config?: AxiosRequestConfig): Promise<Response> {
        return this.fetchDataWithAxios<Response>({
            method: 'GET',
            url,
            ...config
        })
    },

    /**
     * POST request helper
     */
    post<Response = unknown, Request = Record<string, unknown>>(
        url: string, 
        data?: Request, 
        config?: AxiosRequestConfig
    ): Promise<Response> {
        return this.fetchDataWithAxios<Response, Request>({
            method: 'POST',
            url,
            data,
            ...config
        })
    },

    /**
     * PUT request helper
     */
    put<Response = unknown, Request = Record<string, unknown>>(
        url: string, 
        data?: Request, 
        config?: AxiosRequestConfig
    ): Promise<Response> {
        return this.fetchDataWithAxios<Response, Request>({
            method: 'PUT',
            url,
            data,
            ...config
        })
    },

    /**
     * DELETE request helper
     */
    delete<Response = unknown>(url: string, config?: AxiosRequestConfig): Promise<Response> {
        return this.fetchDataWithAxios<Response>({
            method: 'DELETE',
            url,
            ...config
        })
    },

    /**
     * PATCH request helper
     */
    patch<Response = unknown, Request = Record<string, unknown>>(
        url: string, 
        data?: Request, 
        config?: AxiosRequestConfig
    ): Promise<Response> {
        return this.fetchDataWithAxios<Response, Request>({
            method: 'PATCH',
            url,
            data,
            ...config
        })
    },

    /**
     * Upload file helper
     */
    upload<Response = unknown>(
        url: string, 
        file: File, 
        onProgress?: (progress: number) => void,
        config?: AxiosRequestConfig
    ): Promise<Response> {
        const formData = new FormData()
        formData.append('file', file)

        return this.fetchDataWithAxios<Response>({
            method: 'POST',
            url,
            data: formData,
            headers: {
                'Content-Type': 'multipart/form-data',
                ...config?.headers
            },
            onUploadProgress: (progressEvent) => {
                if (onProgress && progressEvent.total) {
                    const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
                    onProgress(progress)
                }
            },
            ...config
        })
    },

    /**
     * Download file helper
     */
    download(url: string, filename?: string, config?: AxiosRequestConfig): Promise<void> {
        return new Promise((resolve, reject) => {
            API({
                method: 'GET',
                url,
                responseType: 'blob',
                ...config
            })
            .then((response: AxiosResponse<Blob>) => {
                const blob = new Blob([response.data])
                const downloadUrl = window.URL.createObjectURL(blob)
                const link = document.createElement('a')
                link.href = downloadUrl
                link.download = filename || 'download'
                document.body.appendChild(link)
                link.click()
                document.body.removeChild(link)
                window.URL.revokeObjectURL(downloadUrl)
                resolve()
            })
            .catch(reject)
        })
    }
}

export default ApiService
