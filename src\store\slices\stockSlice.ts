import { createAsyncThunk, createSlice } from "@reduxjs/toolkit"
import { getStocks, getStockById, createStock as createStockService, updateStock as updateStockService, updateStockStatus as updateStockStatusService, deleteStock as deleteStockService } from "@/services/StockService"
import type { StockRequest, StockColumns, Stock  } from "@/@types/stocks"

export const fetchStocks = createAsyncThunk(
    'stock/fetchStocks',
    async () => {
        return await getStocks()
    }
)

export const fetchStockById = createAsyncThunk(
    'stock/fetchStockById',
    async (id: number) => {
        return await getStockById(id)
    }
)

export const createStockAction = createAsyncThunk(
    'stock/createStock',
    async (stock: StockRequest) => {
        return await createStockService(stock)
    }
)

export const updateStockAction = createAsyncThunk(
    'stock/updateStock',
    async ({id, stock}: {id: number, stock: StockRequest}) => {
        return await updateStockService(id, stock)
    }
)

export const updateStockStatusAction = createAsyncThunk(
    'stock/updateStockStatus',
    async ({id, status}: {id: number, status: number}) => {
        return await updateStockStatusService(id, status)
    }
)

export const deleteStockAction = createAsyncThunk(
    'stock/deleteStock',
    async (id: number) => {
        return await deleteStockService(id)
    }
)

export const stockSlice = createSlice({
    name: 'stock',
    initialState: {
        stocks: [] as StockColumns[],
        stock: null as Stock | null,    
        loading: false,
        error: null as string | null,
    },
    reducers: {
        clearStock: (state) => {
            state.stock = null
        },
    },
    extraReducers: (builder) => {   
        builder
            .addCase(fetchStocks.pending, (state) => {
                state.loading = true
                state.error = null
            })
            .addCase(fetchStocks.fulfilled, (state, action) => {
                state.stocks = action.payload
                state.loading = false
                state.error = null
            })
            .addCase(fetchStocks.rejected, (state, action) => {
                state.loading = false
                state.error = action.error.message || 'Failed to fetch stocks'
            })
            .addCase(fetchStockById.pending, (state) => {
                state.loading = true
                state.error = null
            })
            .addCase(fetchStockById.fulfilled, (state, action) => {
                state.stock = action.payload
                state.loading = false
                state.error = null
            })
            .addCase(fetchStockById.rejected, (state, action) => {
                state.loading = false
                state.error = action.error.message || 'Failed to fetch stock'
            })
            .addCase(createStockAction.pending, (state) => {
                state.loading = true
                state.error = null
            })
            .addCase(createStockAction.fulfilled, (state, action) => {
                state.stocks.push(action.payload)
                state.loading = false
                state.error = null
            })
            .addCase(createStockAction.rejected, (state, action) => {
                state.loading = false
                state.error = action.error.message || 'Failed to create stock'
            })
            .addCase(updateStockAction.pending, (state) => {
                state.loading = true
                state.error = null
            })
            .addCase(updateStockAction.fulfilled, (state, action) => {
                state.stocks = state.stocks.map((stock) =>
                    stock.id === action.payload.id ? action.payload : stock,
                )
                state.loading = false
                state.error = null
            })
            .addCase(updateStockAction.rejected, (state, action) => {
                state.loading = false
                state.error = action.error.message || 'Failed to update stock'
            })
            .addCase(updateStockStatusAction.pending, (state) => {
                state.loading = true
                state.error = null
            })
            .addCase(updateStockStatusAction.fulfilled, (state, action) => {
                state.stocks = state.stocks.map((stock) =>
                    stock.id === action.meta.arg.id ? { ...stock, status: action.meta.arg.status } : stock,
                )
                state.loading = false
                state.error = null
            })
            .addCase(updateStockStatusAction.rejected, (state, action) => {
                state.loading = false
                state.error = action.error.message || 'Failed to update stock status'
            })
            .addCase(deleteStockAction.pending, (state) => {
                state.loading = true
                state.error = null
            })
            .addCase(deleteStockAction.fulfilled, (state, action) => {
                state.stocks = state.stocks.filter((stock) => stock.id !== action.meta.arg)
                state.loading = false
                state.error = null
            })
            .addCase(deleteStockAction.rejected, (state, action) => {
                state.loading = false
                state.error = action.error.message || 'Failed to delete stock'
            })
    },
})

export const { clearStock } = stockSlice.actions
export const stockReducer = stockSlice.reducer