import { create } from 'zustand';
import type { Warehouse, WarehouseFilters, ViewMode } from '../types';
import { MOCK_WAREHOUSES } from '../types';
import { capacity, generateLayout } from '../utils';

interface WarehouseState {
  warehouses: Warehouse[];
  selectedWarehouse: Warehouse | null;
  filters: WarehouseFilters;
  viewMode: ViewMode;
  searchQuery: string;
  selectedBoxId: string | null;
  isLoading: boolean;
  error: string | null;
}

interface WarehouseActions {
  // Warehouse CRUD
  fetchWarehouses: () => Promise<void>;
  createWarehouse: (warehouse: Omit<Warehouse, 'id' | 'createdAt' | 'updatedAt'>) => Promise<void>;
  updateWarehouse: (id: string, updates: Partial<Warehouse>) => Promise<void>;
  deleteWarehouse: (id: string) => Promise<void>;
  
  // Selection and navigation
  selectWarehouse: (warehouse: Warehouse | null) => void;
  setViewMode: (mode: ViewMode) => void;
  setSearchQuery: (query: string) => void;
  selectBox: (boxId: string | null) => void;
  
  // Filtering
  setFilters: (filters: Partial<WarehouseFilters>) => void;
  clearFilters: () => void;
  
  // UI state
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  
  // Computed values
  getFilteredWarehouses: () => Warehouse[];
  getCurrentLayout: () => ReturnType<typeof generateLayout> | null;
  getCurrentCapacity: () => ReturnType<typeof capacity> | null;
}

const initialState: WarehouseState = {
  warehouses: [],
  selectedWarehouse: null,
  filters: {
    search: '',
    minCapacity: 0,
    maxCapacity: Infinity,
    createdAfter: '',
    createdBefore: '',
  },
  viewMode: '2D',
  searchQuery: '',
  selectedBoxId: null,
  isLoading: false,
  error: null,
};

export const useWarehouseStore = create<WarehouseState & WarehouseActions>((set, get) => ({
  ...initialState,

  // Warehouse CRUD
  fetchWarehouses: async () => {
    set({ isLoading: true, error: null });
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 500));
      set({ warehouses: MOCK_WAREHOUSES, isLoading: false });
    } catch (error) {
      console.error('Failed to fetch warehouses:', error);
      set({ error: 'Failed to fetch warehouses', isLoading: false });
    }
  },

  createWarehouse: async (warehouseData) => {
    set({ isLoading: true, error: null });
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 300));
      const newWarehouse: Warehouse = {
        ...warehouseData,
        id: Date.now().toString(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };
      set(state => ({
        warehouses: [...state.warehouses, newWarehouse],
        isLoading: false,
      }));
    } catch (err) {
      console.error('Failed to create warehouse:', err);
      set({ error: 'Failed to create warehouse', isLoading: false });
    }
  },

  updateWarehouse: async (id, updates) => {
    set({ isLoading: true, error: null });
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 300));
      set(state => ({
        warehouses: state.warehouses.map(w =>
          w.id === id ? { ...w, ...updates, updatedAt: new Date().toISOString() } : w
        ),
        selectedWarehouse: state.selectedWarehouse?.id === id 
          ? { ...state.selectedWarehouse, ...updates, updatedAt: new Date().toISOString() }
          : state.selectedWarehouse,
        isLoading: false,
      }));
    } catch (err) {
      console.error('Failed to update warehouse:', err);
      set({ error: 'Failed to update warehouse', isLoading: false });
    }
  },

  deleteWarehouse: async (id) => {
    set({ isLoading: true, error: null });
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 300));
      set(state => ({
        warehouses: state.warehouses.filter(w => w.id !== id),
        selectedWarehouse: state.selectedWarehouse?.id === id ? null : state.selectedWarehouse,
        isLoading: false,
      }));
    } catch (err) {
      console.error('Failed to delete warehouse:', err);
        set({ error: 'Failed to delete warehouse', isLoading: false });
    }
  },

  // Selection and navigation
  selectWarehouse: (warehouse) => {
    set({ selectedWarehouse: warehouse, selectedBoxId: null });
  },

  setViewMode: (mode) => {
    set({ viewMode: mode });
  },

  setSearchQuery: (query) => {
    set({ searchQuery: query });
  },

  selectBox: (boxId) => {
    set({ selectedBoxId: boxId });
  },

  // Filtering
  setFilters: (filters) => {
    set(state => ({
      filters: { ...state.filters, ...filters }
    }));
  },

  clearFilters: () => {
    set({ filters: initialState.filters });
  },

  // UI state
  setLoading: (loading) => {
    set({ isLoading: loading });
  },

  setError: (error) => {
    set({ error });
  },

  // Computed values
  getFilteredWarehouses: () => {
    const { warehouses, filters } = get();
    return warehouses.filter(warehouse => {
      // Search filter
      if (filters.search && !warehouse.name.toLowerCase().includes(filters.search.toLowerCase())) {
        return false;
      }

      // Capacity filters
      const cap = capacity(warehouse);
      if (cap.totalBoxes < filters.minCapacity || cap.totalBoxes > filters.maxCapacity) {
        return false;
      }

      // Date filters
      if (filters.createdAfter && warehouse.createdAt < filters.createdAfter) {
        return false;
      }
      if (filters.createdBefore && warehouse.createdAt > filters.createdBefore) {
        return false;
      }

      return true;
    });
  },

  getCurrentLayout: () => {
    const { selectedWarehouse } = get();
    return selectedWarehouse ? generateLayout(selectedWarehouse) : null;
  },

  getCurrentCapacity: () => {
    const { selectedWarehouse } = get();
    return selectedWarehouse ? capacity(selectedWarehouse) : null;
  },
}));
