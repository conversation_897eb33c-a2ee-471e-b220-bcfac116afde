import Container from '@/components/shared/Container'
import AdaptiveCard from '@/components/shared/AdaptiveCard'
import StockListActionTools from './components/StockListActionTools'
import StockListTableTools from './components/StockListTableTools'
import StockListTable from './components/StockListTable'
import StockListSelected from './components/StockListSelected'
import StockModal from './components/StockModal'
import useTranslation from '@/utils/hooks/useTranslation'
import { useState } from 'react'

const Stocks = () => {
    const { t } = useTranslation()
    const [isEditModalOpen, setIsEditModalOpen] = useState(false)
    const [editingStock_id, setEditingStock] = useState<number | undefined>()

    const handleEdit = (stock_id: number) => {
        setEditingStock(stock_id)
        setIsEditModalOpen(true)
    }

    const handleEditSuccess = () => {
        setIsEditModalOpen(false)
        setEditingStock(undefined)
        // Refresh the list here if needed
    }

    const handleEditClose = () => {
        setIsEditModalOpen(false)
        setEditingStock(undefined)
    }

    return (
        <>
            <Container>
                <AdaptiveCard>
                    <div className="flex flex-col gap-4">
                    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
                    <h3>{t('nav.menus.stocks')}</h3>
                    <StockListActionTools />
                </div>
                <StockListTableTools />
                <StockListTable onEdit={handleEdit} />
                    </div>
                </AdaptiveCard>
            </Container>
            <StockListSelected />
            
            {/* Edit Modal */}
            <StockModal
                isOpen={isEditModalOpen}
                mode="edit"
                stock_id={editingStock_id}
                onSuccess={handleEditSuccess}
                onClose={handleEditClose}
            />
        </>
    )
}

export default Stocks
