import React, { useEffect, useState } from 'react';
import WarehouseList from './components/WarehouseList';
import CreateWarehouse from './components/CreateWarehouse';
import WarehouseDetail from './components/WarehouseDetail';
import { useWarehouseStore } from './store/warehouseStore';
import type { Warehouse } from './types';

type ViewMode = 'list' | 'create' | 'detail';

const WareHouses: React.FC = () => {
  const [currentView, setCurrentView] = useState<ViewMode>('list');
  const [selectedWarehouse, setSelectedWarehouse] = useState<Warehouse | null>(null);
  
  const {
    fetchWarehouses,
  } = useWarehouseStore();

  useEffect(() => {
    fetchWarehouses();
  }, [fetchWarehouses]);

  const handleOpenCreate = () => {
    setCurrentView('create');
  };

  const handleOpenDetail = (warehouse: Warehouse) => {
    setSelectedWarehouse(warehouse);
    setCurrentView('detail');
  };

  const handleBackFromCreate = () => {
    setCurrentView('list');
  };

  const handleBackFromDetail = () => {
    setCurrentView('list');
    setSelectedWarehouse(null);
  };

  const renderCurrentView = () => {
    switch (currentView) {
      case 'create':
        return (
          <CreateWarehouse 
            onClose={handleBackFromCreate}
            onSuccess={() => {
              setCurrentView('list');
            }}
          />
        );
      case 'detail':
        return selectedWarehouse ? (
          <WarehouseDetail 
            warehouse={selectedWarehouse}
            onClose={handleBackFromDetail}
          />
        ) : null;
      default:
        return (
          <WarehouseList 
            onOpenCreate={handleOpenCreate}
            onOpenDetail={handleOpenDetail}
          />
        );
    }
  };

  return (
    <div className="h-full">
      {renderCurrentView()}
    </div>
  );
};

export default WareHouses;
