import { useState } from 'react'
import Button from '@/components/ui/Button'
import StockModal from './StockModal'
import useTranslation from '@/utils/hooks/useTranslation'
import { useStock } from '@/utils/hooks/useStock'
import { TbPlus } from 'react-icons/tb'

const StockListActionTools = () => {
    const { t } = useTranslation()
    const { fetchAllStocks } = useStock()
    const [addModalOpen, setAddModalOpen] = useState(false)

    const handleAddModalClose = () => {
        setAddModalOpen(false)
    }

    const handleAddSuccess = () => {
        setAddModalOpen(false)
        // Refresh the data after successful creation
        fetchAllStocks()
    }

    return (
        <>
            <div className="flex items-center gap-2">
                <Button
                    variant="solid"
                    icon={<TbPlus />}
                    onClick={() => setAddModalOpen(true)}
                >
                    {t('nav.shared.add')}
                </Button>
            </div>

            <StockModal
                mode="add"
                isOpen={addModalOpen}
                onSuccess={handleAddSuccess}
                onClose={handleAddModalClose}
            />
        </>
    )
}

export default StockListActionTools
