/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useEffect, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { Select } from '@/components/ui'
import { FormItem } from '@/components/ui/Form'
import { Controller } from 'react-hook-form'
import { useTranslation } from 'react-i18next'
import {
    fetchCountries,
    fetchGovernorates,
    fetchCities,
    fetchDistricts,
} from '@/store/slices/locationSlice'
import {
    selectCountries,
    selectGovernorates,
    selectCities,
    selectDistricts,
    selectLoadingCountries,
    selectLoadingGovernorates,
    selectLoadingCities,
    selectLoadingDistricts,
    selectCountriesAsOptions,
    selectGovernoratesAsOptions,
    selectCitiesAsOptions,
    selectDistrictsAsOptions,
    getCountryByCode,
    getGovernorateByCode,
    getCityByCode,
    getDistrictByCode,
} from '@/store/selectors/locationSelector'
import type { AppDispatch } from '@/store/store'
import type { Country, Governorate, City, District } from '@/@types/location'

export interface LocationSelectorProps {
    // Form control props
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    control: any
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    errors: any

    // Field names for form binding
    countryField?: string
    governorateField?: string
    cityField?: string
    districtField?: string

    // Labels
    countryLabel?: string
    governorateLabel?: string
    cityLabel?: string
    districtLabel?: string

    // Placeholders
    countryPlaceholder?: string
    governoratePlaceholder?: string
    cityPlaceholder?: string
    districtPlaceholder?: string

    // Which fields to show
    showCountry?: boolean
    showGovernorate?: boolean
    showCity?: boolean
    showDistrict?: boolean

    // Layout
    className?: string
    gridCols?: string

    // Callbacks for getting data by code
    onCountryChange?: (countryCode: string, country?: Country) => void
    onGovernorateChange?: (
        governorateCode: string,
        governorate?: Governorate,
    ) => void
    onCityChange?: (cityCode: string, city?: City) => void
    onDistrictChange?: (districtCode: string, district?: District) => void
}

const LocationSelector: React.FC<LocationSelectorProps> = ({
    control,
    errors,
    countryField = 'countryNameAr',
    governorateField = 'governorateNameAr',
    cityField = 'cityOrDistrictNameAr',
    districtField = 'villageNameAr',
    countryLabel,
    governorateLabel,
    cityLabel,
    districtLabel,
    countryPlaceholder,
    governoratePlaceholder,
    cityPlaceholder,
    districtPlaceholder,
    showCountry = true,
    showGovernorate = true,
    showCity = true,
    showDistrict = true,
    className = '',
    gridCols = 'grid-cols-1 md:grid-cols-2',
    onCountryChange,
    onGovernorateChange,
    onCityChange,
    onDistrictChange,
}) => {
    const { t } = useTranslation()
    const dispatch = useDispatch<AppDispatch>()

    // Redux state using selectors
    const countries = useSelector(selectCountries)
    const governorates = useSelector(selectGovernorates)
    const cities = useSelector(selectCities)
    const districts = useSelector(selectDistricts)

    const isLoadingCountries = useSelector(selectLoadingCountries)
    const isLoadingGovernorates = useSelector(selectLoadingGovernorates)
    const isLoadingCities = useSelector(selectLoadingCities)
    const isLoadingDistricts = useSelector(selectLoadingDistricts)

    // Local state for tracking selections
    const [selectedCountry, setSelectedCountry] = useState<string>('')
    const [selectedGovernorate, setSelectedGovernorate] = useState<string>('')
    const [selectedCity, setSelectedCity] = useState<string>('')

    // Load countries on mount
    useEffect(() => {
        if (countries.length === 0) {
            dispatch(fetchCountries())
        }
    }, [dispatch, countries.length])

    // Get select options using selectors
    const countryOptions = useSelector(selectCountriesAsOptions)
    const governorateOptions = useSelector(selectGovernoratesAsOptions)
    const cityOptions = useSelector(selectCitiesAsOptions)
    const districtOptions = useSelector(selectDistrictsAsOptions)

    // Handle cascading changes
    const handleCountryChange = async (
        countryCode: string,
        onChange: (value: string) => void,
    ) => {
        setSelectedCountry(countryCode)
        setSelectedGovernorate('')
        setSelectedCity('')
        onChange(countryCode)

        if (countryCode) {
            await dispatch(fetchGovernorates(countryCode))
            const country = getCountryByCode(countries, countryCode)
            onCountryChange?.(countryCode, country)
        }
    }

    const handleGovernorateChange = async (
        governorateCode: string,
        onChange: (value: string) => void,
    ) => {
        setSelectedGovernorate(governorateCode)
        setSelectedCity('')
        onChange(governorateCode)

        if (governorateCode) {
            await dispatch(fetchCities(governorateCode))
            const governorate = getGovernorateByCode(
                governorates,
                governorateCode,
            )
            onGovernorateChange?.(governorateCode, governorate)
        }
    }

    const handleCityChange = async (
        cityCode: string,
        onChange: (value: string) => void,
    ) => {
        setSelectedCity(cityCode)
        onChange(cityCode)

        if (cityCode) {
            await dispatch(fetchDistricts(cityCode))
            const city = getCityByCode(cities, cityCode)
            onCityChange?.(cityCode, city)
        }
    }

    const handleDistrictChange = (
        districtCode: string,
        onChange: (value: string) => void,
    ) => {
        onChange(districtCode)
        const district = getDistrictByCode(districts, districtCode)
        onDistrictChange?.(districtCode, district)
    }

    return (
        <div className={`grid ${gridCols} gap-4 ${className}`}>
            {showCountry && (
                <FormItem
                    className="mb-4"
                    label={countryLabel || t('nav.buildings.country')}
                    invalid={Boolean(errors[countryField])}
                    errorMessage={errors[countryField]?.message}
                >
                    <Controller
                        name={countryField}
                        control={control}
                        render={({ field }) => (
                            <Select
                                options={countryOptions}
                                placeholder={
                                    countryPlaceholder ||
                                    t('nav.buildings.selectCountry')
                                }
                                isLoading={isLoadingCountries}
                                value={countryOptions.filter(
                                    (option) => option.value === field.value,
                                )}
                                onChange={(option) =>
                                    handleCountryChange(
                                        option?.value || '',
                                        field.onChange,
                                    )
                                }
                            />
                        )}
                    />
                </FormItem>
            )}

            {showGovernorate && (
                <FormItem
                    className="mb-4"
                    label={governorateLabel || t('nav.buildings.governorate')}
                    invalid={Boolean(errors[governorateField])}
                    errorMessage={errors[governorateField]?.message}
                >
                    <Controller
                        name={governorateField}
                        control={control}
                        render={({ field }) => (
                            <Select
                                options={governorateOptions}
                                placeholder={
                                    governoratePlaceholder ||
                                    t('nav.buildings.selectGovernorate')
                                }
                                isLoading={isLoadingGovernorates}
                                isDisabled={
                                    !selectedCountry ||
                                    governorateOptions.length === 0
                                }
                                value={governorateOptions.filter(
                                    (option) => option.value === field.value,
                                )}
                                onChange={(option) =>
                                    handleGovernorateChange(
                                        option?.value || '',
                                        field.onChange,
                                    )
                                }
                            />
                        )}
                    />
                </FormItem>
            )}

            {showCity && (
                <FormItem
                    className="mb-4"
                    label={cityLabel || t('nav.buildings.city')}
                    invalid={Boolean(errors[cityField])}
                    errorMessage={errors[cityField]?.message}
                >
                    <Controller
                        name={cityField}
                        control={control}
                        render={({ field }) => (
                            <Select
                                options={cityOptions}
                                placeholder={
                                    cityPlaceholder ||
                                    t('nav.buildings.selectCity')
                                }
                                isLoading={isLoadingCities}
                                isDisabled={
                                    !selectedGovernorate ||
                                    cityOptions.length === 0
                                }
                                value={cityOptions.filter(
                                    (option) => option.value === field.value,
                                )}
                                onChange={(option) =>
                                    handleCityChange(
                                        option?.value || '',
                                        field.onChange,
                                    )
                                }
                            />
                        )}
                    />
                </FormItem>
            )}

            {showDistrict && (
                <FormItem
                    className="mb-4"
                    label={districtLabel || t('nav.buildings.village')}
                    invalid={Boolean(errors[districtField])}
                    errorMessage={errors[districtField]?.message}
                >
                    <Controller
                        name={districtField}
                        control={control}
                        render={({ field }) => (
                            <Select
                                options={districtOptions}
                                placeholder={
                                    districtPlaceholder ||
                                    t('nav.buildings.selectVillage')
                                }
                                isLoading={isLoadingDistricts}
                                isDisabled={
                                    !selectedCity ||
                                    districtOptions.length === 0
                                }
                                value={districtOptions.filter(
                                    (option) => option.value === field.value,
                                )}
                                onChange={(option) =>
                                    handleDistrictChange(
                                        option?.value || '',
                                        field.onChange,
                                    )
                                }
                            />
                        )}
                    />
                </FormItem>
            )}
        </div>
    )
}

export default LocationSelector
