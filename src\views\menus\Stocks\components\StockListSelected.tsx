import { useState } from 'react'
import StickyFooter from '@/components/shared/StickyFooter'
import Button from '@/components/ui/Button'
import ConfirmDialog from '@/components/shared/ConfirmDialog'
import { useStock } from '@/utils/hooks/useStock'
import { TbChecks } from 'react-icons/tb'
import useTranslation from '@/utils/hooks/useTranslation'

const StockListSelected = () => {
    const { t } = useTranslation()
    const {
        selectedStocks,
        bulkDeleteStocks,
        bulkUpdateStatus,
    } = useStock()

    const [deleteConfirmationOpen, setDeleteConfirmationOpen] = useState(false)
    const [statusChangeConfirmationOpen, setStatusChangeConfirmationOpen] = useState(false)
    const [targetStatus, setTargetStatus] = useState<number>(1)

    const handleDelete = () => {
        setDeleteConfirmationOpen(true)
    }

    const handleCancel = () => {
        setDeleteConfirmationOpen(false)
    }

    const handleConfirmDelete = async () => {
        try {
            await bulkDeleteStocks()
            setDeleteConfirmationOpen(false)
        } catch (error) {
            console.error('Failed to delete stocks:', error)
        }
    }

    const handleStatusChange = (status: number) => {
        setTargetStatus(status)
        setStatusChangeConfirmationOpen(true)
    }

    const handleStatusChangeCancel = () => {
        setStatusChangeConfirmationOpen(false)
    }

    const handleConfirmStatusChange = async () => {
        try {
            await bulkUpdateStatus(targetStatus)
            setStatusChangeConfirmationOpen(false)
        } catch (error) {
            console.error('Failed to update stock status:', error)
        }
    }

    return (
        <>
            {selectedStocks.length > 0 && (
                <StickyFooter
                    className="flex items-center justify-between py-4 bg-white dark:bg-gray-800"
                    stickyClass="-mx-4 sm:-mx-8 border-t border-gray-200 dark:border-gray-700 px-8"
                    defaultClass="container mx-auto px-8 rounded-xl border border-gray-200 dark:border-gray-600 mt-4"
                >
                    <div className="container mx-auto">
                        <div className="flex items-center justify-between">
                            <span>
                                <span className="flex items-center gap-2">
                                    <span className="text-lg text-primary">
                                        <TbChecks />
                                    </span>
                                    <span className="font-semibold flex items-center gap-1">
                                        <span className="heading-text">
                                            {selectedStocks.length}{' '}
                                            {t('nav.menus.stocks')}
                                        </span>
                                        <span>{t('nav.shared.selected')}</span>
                                    </span>
                                </span>
                            </span>

                            <div className="flex items-center gap-2">
                                <Button
                                    size="sm"
                                    variant="solid"
                                    onClick={() => handleStatusChange(1)}
                                    className="bg-green-600 hover:bg-green-700"
                                >
                                    {t('nav.shared.active')}
                                </Button>
                                <Button
                                    size="sm"
                                    variant="solid"
                                    onClick={() => handleStatusChange(0)}
                                    className="bg-red-600 hover:bg-red-700"
                                >
                                    {t('nav.shared.inactive')}
                                </Button>
                                <Button
                                    size="sm"
                                    className="ltr:mr-3 rtl:ml-3"
                                    type="button"
                                    customColorClass={() =>
                                        'border-error ring-1 ring-error text-error hover:border-error hover:ring-error hover:text-error'
                                    }
                                    onClick={handleDelete}
                                >
                                    {t('nav.shared.delete')}
                                </Button>
                            </div>
                        </div>
                    </div>
                </StickyFooter>
            )}

            <ConfirmDialog
                isOpen={deleteConfirmationOpen}
                type="danger"
                title={t('nav.shared.deleteStock')}
                onClose={handleCancel}
                onRequestClose={handleCancel}
                onCancel={handleCancel}
                onConfirm={handleConfirmDelete}
            >
                <p>{t('nav.shared.confirmDeleteStock')}</p>
            </ConfirmDialog>

            <ConfirmDialog
                isOpen={statusChangeConfirmationOpen}
                type="info"
                title={t('nav.shared.updateStatus')}
                onClose={handleStatusChangeCancel}
                onRequestClose={handleStatusChangeCancel}
                onCancel={handleStatusChangeCancel}
                onConfirm={handleConfirmStatusChange}
            >
                <p>
                    Are you sure you want to change the status of {selectedStocks.length} stocks to {targetStatus === 1 ? t('nav.shared.active') : t('nav.shared.inactive')}?
                </p>
            </ConfirmDialog>
        </>
    )
}

export default StockListSelected
