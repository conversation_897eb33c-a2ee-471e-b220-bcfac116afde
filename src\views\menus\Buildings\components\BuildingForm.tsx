import { useEffect } from 'react'
import { Form } from '@/components/ui/Form'
import Container from '@/components/shared/Container'
import BottomStickyBar from '@/components/template/BottomStickyBar'
import BasicInfoSection from './BasicInfoSection'
import { useForm } from 'react-hook-form'
import { z } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import isEmpty from 'lodash/isEmpty'
import type { CommonProps } from '@/@types/common'
import type { Building } from '../../../../services/BuildingService'

type BuildingFormProps = {
    onFormSubmit: (values: Building) => void
    defaultValues?: Partial<Building>
    newBuilding?: boolean
} & CommonProps

// Create a partial validation schema for form submission
const validationSchema = z.object({
    name: z.string().min(1, { message: 'Name is required!' }),
    villageNameAr: z.string().min(1, { message: 'Village name is required!' }),
    cityOrDistrictNameAr: z
        .string()
        .min(1, { message: 'City/District name is required!' }),
    governorateNameAr: z
        .string()
        .min(1, { message: 'Governorate name is required!' }),
    countryNameAr: z.string().min(1, { message: 'Country name is required!' }),
    status: z.number().min(0).max(1, { message: 'Status is required!' }),
})

const BuildingForm = (props: BuildingFormProps) => {
    const { onFormSubmit, defaultValues = {}, children } = props

    const {
        handleSubmit,
        reset,
        formState: { errors },
        control,
    } = useForm<Building>({
        defaultValues: {
            ...defaultValues,
        },
        resolver: zodResolver(validationSchema),
    })

    useEffect(() => {
        if (!isEmpty(defaultValues)) {
            reset(defaultValues)
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [JSON.stringify(defaultValues)])

    const onSubmit = (values: Building) => {
        onFormSubmit?.(values)
    }

    return (
        <Form
            className="flex w-full h-full "
            containerClassName="flex flex-col w-full justify-between gap-4"
            onSubmit={handleSubmit(onSubmit)}
        >
            <Container>
                <div className="flex flex-col sm:flex-row justify-between gap-4 mt-4">
                    <BasicInfoSection control={control} errors={errors} />
                </div>
            </Container>
            <BottomStickyBar>{children}</BottomStickyBar>
        </Form>
    )
}

export default BuildingForm
