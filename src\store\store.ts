import {configureStore} from '@reduxjs/toolkit'
import { buildingReducer } from './slices/buildingSlice'
import { locationReducer } from './slices/locationSlice'
import { stockReducer } from './slices/stockSlice'


export const store = configureStore({
    reducer: {
        building: buildingReducer,
        location: locationReducer,
        stock: stockReducer,
    },
})

export type RootState = ReturnType<typeof store.getState>
export type AppDispatch = typeof store.dispatch
