
import { Form, FormItem } from '@/components/ui/Form'
import Input from '@/components/ui/Input'
import Button from '@/components/ui/Button'
import useTranslation from '@/utils/hooks/useTranslation'
import { Controller, useForm } from 'react-hook-form'
import { z } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import type { StockRequest , StockPhone } from '@/@types/stocks'
import { Card } from '@/components/ui'
import { useEffect, useState } from 'react'
import SimpleLocationSelector from '@/components/shared/SimpleLocationSelector'
import { useStock } from '@/utils/hooks/useStock'

interface StockFormProps {
    onSubmit: (data: StockRequest) => Promise<void>
    isSubmitting: boolean
    onCancel: () => void
    stock_id?: number
    mode: 'add' | 'edit'
}

// Create a validation schema for form submission
const validationSchema = z.object({
    name: z.string().min(1, { message: 'Name is required!' }),
    managerName: z.string().min(1, { message: 'Manager name is required!' }),
    villageCode: z.string().min(1, { message: 'Village code is required!' }),
    address: z.string().optional(),
    maintenancePhone: z.string().optional(),
    securityPhone: z.string().optional(),
    email: z.string().email({ message: 'Invalid email format!' }).optional().or(z.literal('')),
    description: z.string().optional(),
    stockPhones: z.array(z.object({
        phoneNumber: z.string().min(1, { message: 'Phone number is required!' }),
        notes: z.string().optional()
    })).optional(),
})

const StockForm = ({ onSubmit, isSubmitting, onCancel, stock_id, mode }: StockFormProps) => {
    const { t } = useTranslation()

    const { fetchStock , stock, loading: stockLoading, clearStockData , updateStockStatus} = useStock()

    useEffect(() => {
        if (stock_id) {
            fetchStock(stock_id)
        }
    }, [stock_id, fetchStock])

    // Clear stock data when component unmounts or mode changes
    useEffect(() => {
        return () => {
            // Clear stock data when component unmounts
            clearStockData()
        }
    }, [clearStockData])

    // Clear stock data when switching to add mode
    useEffect(() => {
        if (mode === 'add') {
            clearStockData()
        }
    }, [mode, clearStockData])

    const editData = stock
    
    console.log('StockForm - stock_id:', stock_id)
    console.log('StockForm - stock data:', stock)
    console.log('StockForm - editData:', editData)
    
    const [phoneInput, setPhoneInput] = useState<StockPhone>({
        phoneNumber: '',
        notes: ''
    })
    
    const {
        handleSubmit,
        formState: { errors },
        control,
        reset,
    } = useForm<StockRequest>({
        defaultValues: {
            name: '',
            managerName: '',
            villageCode: '',
            address: '',
            maintenancePhone: '',
            securityPhone: '',
            email: '',
            description: '',
            stockPhones: [],
        },
        resolver: zodResolver(validationSchema),
    })

    // Update form when stock data changes
    useEffect(() => {
        console.log('Form reset effect triggered:', { editData, stockLoading, mode })
        if (editData && !stockLoading) {
            console.log('Resetting form with edit data:', editData)
            reset({
                name: editData.name || '',
                managerName: editData.managerName || '',
                villageCode: editData.villageCode || '',
                address: editData.address || '',
                maintenancePhone: editData.maintenancePhone || '',
                securityPhone: editData.securityPhone || '',
                email: editData.email || '',
                description: editData.description || '',
                stockPhones: editData.stockPhones || [],
            })
        } else if (mode === 'add') {
            console.log('Resetting form for add mode')
            // Reset form to empty when adding new stock
            reset({
                name: '',
                managerName: '',
                villageCode: '',
                address: '',
                maintenancePhone: '',
                securityPhone: '',
                email: '',
                description: '',
                stockPhones: [],
            })
        }
    }, [editData, reset, mode, stockLoading])


    const onFormSubmit = async (values: StockRequest) => {
        const stockData: StockRequest = {
            name: values.name,
            managerName: values.managerName,
            villageCode: values.villageCode,
            address: values.address || '',
            maintenancePhone: values.maintenancePhone || '',
            securityPhone: values.securityPhone || '',
            email: values.email || '',
            description: values.description || '',
            stockPhones: values.stockPhones || [],
        }
        await onSubmit(stockData)
    }

    const onApprove = async () => {
        await updateStockStatus(stock_id!, 1)
    }

    // Show loading state while fetching stock data
    if (mode === 'edit' && stockLoading) {
        return (
            <div className="flex items-center justify-center p-8">
                <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
                    <p className="text-gray-600">Loading stock data...</p>
                </div>
            </div>
        )
    }

    return (
        <Form
            className="flex w-full gap-4"
            containerClassName="flex flex-col w-full gap-4"
            onSubmit={handleSubmit(onFormSubmit)}
        >
            <div className="flex flex-col gap-4 p-6">
                <Card className='w-full p-6'>
                    <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                <FormItem
                    className="mb-4"
                    label={t('nav.Stock.stockName')}
                    invalid={Boolean(errors.name)}
                    errorMessage={errors.name?.message}
                >
                    <Controller
                        name="name"
                        control={control}
                        render={({ field }) => (
                            <Input
                                type="text"
                                autoComplete="off"
                                placeholder={t('nav.Stock.enterStockName')}
                                {...field}
                            />
                        )}
                    />
                </FormItem>

                        <FormItem
                            className="mb-4"
                            label={t('nav.Stock.stockManager')}
                            invalid={Boolean(errors.managerName)}
                            errorMessage={errors.managerName?.message}
                        >
                            <Controller
                                name="managerName"
                                control={control}
                                render={({ field }) => (
                                    <Input
                                        type="text"
                                        autoComplete="off"
                                        placeholder={t('nav.Stock.enterStockManager')}
                                        {...field}
                                    />
                                )}
                            />
                        </FormItem>
                    </div>
                    
                    <div className='grid grid-cols-1 gap-4'>
{/* location selector */}

    <SimpleLocationSelector
        control={control}
        errors={errors}
        className="w-full"
        gridCols="grid-cols-1 sm:grid-cols-2 md:grid-cols-4"
        onLocationChange={(locationCode, locationType) => {
            console.log(`Selected ${locationType}: ${locationCode}`)
            
        }}
/>

                    </div>
                    
                    <FormItem
                        className="mb-4"
                        label={t('nav.Stock.address')}
                        invalid={Boolean(errors.address)}
                        errorMessage={errors.address?.message}
                    >
                        <Controller
                            name="address"
                            control={control}
                            render={({ field }) => (
                                <Input
                                    type="text"
                                    autoComplete="off"
                                    placeholder={t('nav.Stock.enterStockAddress')}
                                    {...field}
                                />
                            )}
                        />
                    </FormItem>
                    
                    <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                        <FormItem
                            className="mb-4"
                            label={t('nav.Stock.maintenancePhone')}
                            invalid={Boolean(errors.maintenancePhone)}
                            errorMessage={errors.maintenancePhone?.message}
                        >
                            <Controller
                                name="maintenancePhone"
                                control={control}
                                render={({ field }) => (
                                    <Input
                                        type="text"
                                        autoComplete="off"
                                        placeholder={t('nav.Stock.enterStockMaintenancePhone')}
                                        {...field}
                                    />
                                )}
                            />
                        </FormItem>

                        <FormItem
                            className="mb-4"
                            label={t('nav.Stock.securityPhone')}
                            invalid={Boolean(errors.securityPhone)}
                            errorMessage={errors.securityPhone?.message}
                        >
                            <Controller
                                name="securityPhone"
                                control={control}
                                render={({ field }) => (
                                    <Input
                                        type="text"
                                        autoComplete="off"
                                        placeholder={t('nav.Stock.enterStockSecurityPhone')}
                                        {...field}
                                    />
                                )}
                            />
                        </FormItem>
                                        </div>
                    
                    <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
                        <FormItem
                            className="mb-4"
                            label={t('nav.Stock.email')}
                            invalid={Boolean(errors.email)}
                            errorMessage={errors.email?.message}
                        >
                            <Controller
                                name="email"
                                control={control}
                                render={({ field }) => (
                                    <Input
                                        type="text"
                                        autoComplete="off"
                                        placeholder={t('nav.Stock.enterStockEmail')}
                                        {...field}
                                    />
                                )}
                            />
                        </FormItem>

                        <FormItem
                            className="mb-4"
                            label={t('nav.Stock.description')}
                            invalid={Boolean(errors.description)}
                            errorMessage={errors.description?.message}
                        >
                            <Controller
                                name="description"
                                control={control}
                                render={({ field }) => (
                                    <Input
                                        type="text"
                                        autoComplete="off"
                                        placeholder={t('nav.Stock.enterStockDescription')}
                                        {...field}
                                    />
                                )}
                            />
                        </FormItem>
                    </div>
                    
                    <FormItem
                        className="mb-4"
                        label={t('nav.Stock.stockPhones')}
                        invalid={Boolean(errors.stockPhones)}
                        errorMessage={errors.stockPhones?.message}
                    >
                    <Controller
                        name="stockPhones"
                        control={control}
                        render={({ field }) => (
                            <div className="space-y-3">
                                {field.value && field.value.length > 0 && (
                                    <div className="space-y-2 max-h-32 overflow-y-auto border border-gray-200 rounded-lg p-3 bg-gray-50">
                                        {field.value.map((phone: StockPhone, index: number) => (
                                            <div key={index} className="flex items-center gap-2 p-2 border border-gray-300 rounded-md bg-white">
                                                <div className="flex-1">
                                                    <div className="text-sm font-medium text-gray-700 mb-1">
                                                        {phone.phoneNumber}
                                                    </div>
                                                    <div className="text-sm text-gray-500">
                                                        {phone.notes}
                                                    </div>
                                                </div>
                                                <Button
                                                    type="button"
                                                    size="sm"
                                                    variant="solid"
                                                    className="bg-red-500 hover:bg-red-600 text-white"
                                                    onClick={() => {
                                                        const newPhones = field.value.filter((_: StockPhone, i: number) => i !== index);
                                                        field.onChange(newPhones);
                                                    }}
                                                >
                                                    {t('nav.shared.remove')}
                                                </Button>
                                            </div>
                                        ))}
                                    </div>
                                )}
                                
                                <div className="space-y-2">
                                    <div className="flex gap-2">
                                        <Input
                                            type="tel"
                                            placeholder={t('nav.Stock.enterStockPhone')}
                                            value={phoneInput.phoneNumber}
                                            onChange={(e) => setPhoneInput(prev => ({ ...prev, phoneNumber: e.target.value }))}
                                            className="flex-1"
                                        />
                                        <Input
                                            type="text"
                                            placeholder={t('nav.Stock.enterStockNotes')}
                                            value={phoneInput.notes}
                                            onChange={(e) => setPhoneInput(prev => ({ ...prev, notes: e.target.value }))}
                                            className="flex-1"
                                        />
                                        <Button
                                            type="button"
                                            size="sm"
                                            variant="solid"
                                            disabled={!phoneInput.phoneNumber.trim()}
                                            onClick={() => {
                                                if (phoneInput.phoneNumber.trim()) {
                                                    const newPhones = [...(field.value || []), { ...phoneInput }];
                                                    field.onChange(newPhones);
                                                    setPhoneInput({ phoneNumber: '', notes: '' });
                                                }
                                            }}
                                        >
                                            {t('nav.shared.add')}
                                        </Button>
                                    </div>
                                </div>
                            </div>
                        )}
                    />
                </FormItem>
                </Card>
            </div>
            
            <div className="flex items-center justify-end gap-2 px-6 py-4 bg-white dark:bg-gray-800 border-t border-gray-200">
                <Button
                    size="sm"
                    variant="plain"
                    disabled={isSubmitting}
                    onClick={onCancel}
                >
                    {t('nav.shared.cancel')}
                </Button>
                <Button
                    size="sm"
                    variant="default"
                    disabled={isSubmitting}
                    onClick={onApprove}
                >
                    {t('nav.shared.approve')}
                </Button>
                    <Button
                     size="sm"
                     variant="solid"
                     type="submit"
                     loading={isSubmitting}
                 >
                     {mode === 'add' ? t('nav.shared.create') : t('nav.shared.update')}
                 </Button>
            </div>
        </Form>
    )
}

export default StockForm
