export type Warehouse = {
  id: string;
  name: string;
  description?: string;
  areasCount: number;        // A
  unitsPerArea: number;      // U
  shelvesPerUnit: number;    // S
  boxesPerShelf: number;     // B
  city?: string;
  governorate?: string;
  address?: string;
  phones?: string[];
  emails?: string[];
  existingBoxes?: number;
  createdAt: string;
  updatedAt: string;
};

export type BoxStatus = 'filled' | 'empty';

export type Box = {
  id: string;
  status: BoxStatus;
  content?: string; // Optional content description for filled boxes
};

export type Layout = {
  areas: Array<{
    id: string;              // A1
    units: Array<{
      id: string;            // A1-U1
      shelves: Array<{
        id: string;          // A1-U1-S1
        boxes: Array<Box>;   // A1-U1-S1-B1 with status
      }>;
    }>;
  }>;
};

export type LocationIndices = {
  areaIdx: number;
  unitIdx: number;
  shelfIdx: number;
  boxIdx: number;
};

export type Capacity = {
  totalAreas: number;
  totalUnits: number;
  totalShelves: number;
  totalBoxes: number;
  filledBoxes: number;
  emptyBoxes: number;
  utilizationRate: number; // percentage of filled boxes
};

export type WarehouseFilters = {
  search: string;
  minCapacity: number;
  maxCapacity: number;
  createdAfter: string;
  createdBefore: string;
  city?: string;
  governorate?: string;
};

export type ViewMode = '2D' | 'SETTINGS';

// Constants for validation
export const WAREHOUSE_LIMITS = {
  MAX_AREAS: 50,
  MAX_UNITS_PER_AREA: 100,
  MAX_SHELVES_PER_UNIT: 50,
  MAX_BOXES_PER_SHELF: 100,
  MIN_AREAS: 1,
  MIN_UNITS_PER_AREA: 1,
  MIN_SHELVES_PER_UNIT: 1,
  MIN_BOXES_PER_SHELF: 1,
} as const;

// Mock data for development
export const MOCK_WAREHOUSES: Warehouse[] = [
  {
    id: '1',
    name: 'Main Archive',
    description: 'Primary storage facility for historical documents',
    areasCount: 3,
    unitsPerArea: 4,
    shelvesPerUnit: 6,
    boxesPerShelf: 12,
    city: 'Cairo',
    governorate: 'Cairo',
    address: '123 Main St, Cairo',
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z',
    phones: ['+201234567890'],
    emails: ['<EMAIL>'],
    existingBoxes: 1000,
  },
  {
    id: '2',
    name: 'Secondary Storage',
    description: 'Backup storage for overflow documents',
    areasCount: 2,
    unitsPerArea: 3,
    shelvesPerUnit: 4,
    boxesPerShelf: 8,
    city: 'Alexandria',
    governorate: 'Alexandria',
    address: '456 Coastal Rd, Alexandria',
    createdAt: '2024-02-20T14:30:00Z',
    updatedAt: '2024-02-20T14:30:00Z',
    phones: ['+201234567891'],
    emails: ['<EMAIL>'],
    existingBoxes: 500,
  },
  {
    id: '3',
    name: 'Digital Archive',
    description: 'Storage for digitized documents and media',
    areasCount: 1,
    unitsPerArea: 2,
    shelvesPerUnit: 3,
    boxesPerShelf: 6,
    city: 'Giza',
    governorate: 'Giza',
    address: '789 Pyramid St, Giza',
    createdAt: '2024-03-10T09:15:00Z',
    updatedAt: '2024-03-10T09:15:00Z',
    phones: ['+201234567892'],
    emails: ['<EMAIL>'],
    existingBoxes: 200,
  },
];
