```jsx
import { useState } from 'react'
import Tabs from '@/components/ui/Tabs'

const { Tab<PERSON><PERSON>, TabList, TabContent } = Tabs

const ControlledTabs = () => {
    const [currentTab, setCurrentTab] = useState('tab1')

    return (
        <div>
            <Tabs value={currentTab} onChange={(val) => setCurrentTab(val)}>
                <TabList>
                    <TabNav value="tab1">Home</TabNav>
                    <TabNav value="tab2">Profile</TabNav>
                    <TabNav value="tab3">Contact</TabNav>
                </TabList>
                <div className="p-4">
                    <TabContent value="tab1">
                        <p>
                            If builders built buildings the way programmers
                            wrote programs, then the first woodpecker that came
                            along would destroy civilization. (<PERSON>)
                        </p>
                    </TabContent>
                    <TabContent value="tab2">
                        <p>
                            A computer lets you make more mistakes faster than
                            any invention in human history with the possible
                            exceptions of handguns and tequila. (<PERSON>).
                        </p>
                    </TabContent>
                    <TabContent value="tab3">
                        <p>
                            In C++ its harder to shoot yourself in the foot, but
                            when you do, you blow off your whole leg. (<PERSON><PERSON>ne
                            Stroustrup)
                        </p>
                    </TabContent>
                </div>
            </Tabs>
        </div>
    )
}

export default ControlledTabs
```
