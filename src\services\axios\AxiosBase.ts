import axios from 'axios'
import AxiosResponseInterceptorErrorCallback from './AxiosResponseInterceptorErrorCallback'
import AxiosRequestInterceptorConfigCallback from './AxiosRequestInterceptorConfigCallback'
import type { AxiosError } from 'axios'

const API = axios.create({
    timeout: 60000,
    baseURL: 'https://archiving-system.runasp.net/api' ,
})

API.interceptors.request.use(
    (config) => {
        return AxiosRequestInterceptorConfigCallback(config)
    },
    (error) => {
        return Promise.reject(error)
    },
)

API.interceptors.response.use(
    (response) => response,
    (error: AxiosError) => {
        AxiosResponseInterceptorErrorCallback(error)
        return Promise.reject(error)
    },
)

export default API
