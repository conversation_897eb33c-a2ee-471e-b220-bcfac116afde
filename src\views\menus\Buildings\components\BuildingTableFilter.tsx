import React, { useState } from 'react'
import Button from '@/components/ui/Button'
import Drawer from '@/components/ui/Drawer'
import Checkbox from '@/components/ui/Checkbox'
import Badge from '@/components/ui/Badge'
import Select, { Option as DefaultOption } from '@/components/ui/Select'
import { components } from 'react-select'
import { Form, FormItem } from '@/components/ui/Form'
import useBuildingList from '../hooks/useBuildingList'
import { TbFilter } from 'react-icons/tb'
import { useForm, Controller } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { z } from 'zod'
import type { ZodType } from 'zod'
import type { ControlProps, OptionProps } from 'react-select'
import classNames from '@/utils/classNames'
import useTranslation from '@/utils/hooks/useTranslation'

type FormSchema = {
    buildingStatus: string
    visibleColumns: string[]
}

type Option = {
    value: string
    label: string
    className: string
}

const buildingStatusOption: Option[] = [
    { value: '', label: 'معتمد وغير معتمد', className: 'bg-gray-500 mr-2' },
    { value: '1', label: 'معتمد', className: 'bg-emerald-500 mr-2' },
    { value: '0', label: 'غير معتمد', className: 'bg-red-500 mr-2' },
]


const availableColumns = [
    { value: 'name', label: 'Name' },
    { value: 'villageNameAr', label: 'Village' },
    { value: 'cityOrDistrictNameAr', label: 'City/District' },
    { value: 'governorateNameAr', label: 'Governorate' },
    { value: 'countryNameAr', label: 'Country' },
    { value: 'status', label: 'Status' },
]

const CustomSelectOption = (props: OptionProps<Option>) => {
    return (
        <DefaultOption<Option>
            {...props}
            customLabel={(data, label) => (
                <span className="flex items-center gap-2">
                    <Badge className={data.className} />
                    <span className="ml-2 rtl:mr-2">{label}</span>
                </span>
            )}
        />
    )
}

const CustomControl = ({ children, ...props }: ControlProps<Option>) => {
    const selected = props.getValue()[0]
    const ControlComponent = components.Control as React.ComponentType<
        ControlProps<Option>
    >
    return (
        <ControlComponent {...props}>
            {selected && (
                <Badge className={classNames('ml-4', selected.className)} />
            )}
            {children}
        </ControlComponent>
    )
}

const validationSchema: ZodType<FormSchema> = z.object({
    buildingStatus: z.string(),
    visibleColumns: z.array(z.string()),
})

const BuildingTableFilter = () => {
    
    const [filterIsOpen, setFilterIsOpen] = useState(false)
    const { t } = useTranslation()
    const { filterData, setTableData } = useBuildingList()

    const { handleSubmit, control } = useForm<FormSchema>({
        defaultValues: filterData,
        resolver: zodResolver(validationSchema),
    })

    const onSubmit = (values: FormSchema) => {
        setTableData(values)
        setFilterIsOpen(false)
    }

    return (
        <>
            <Button icon={<TbFilter />} onClick={() => setFilterIsOpen(true)}>
                {t('nav.shared.filter')}
            </Button>
            <Drawer
                title={t('nav.shared.filter')}
                isOpen={filterIsOpen}
                onClose={() => setFilterIsOpen(false)}
                onRequestClose={() => setFilterIsOpen(false)}
            >
                <Form
                    className="h-full"
                    containerClassName="flex flex-col justify-between h-full"
                    onSubmit={handleSubmit(onSubmit)}
                >
                    <div>
                        <FormItem>
                            <Controller
                                name="buildingStatus"
                                control={control}
                                render={({ field }) => (
                                    <Select<Option>
                                        options={buildingStatusOption}
                                        placeholder="Select status..."
                                        {...field}
                                        value={buildingStatusOption.filter(
                                            (option) =>
                                                option.value === field.value,
                                        )}
                                        components={{
                                            Option: CustomSelectOption,
                                            Control: CustomControl,
                                        }}
                                        onChange={(option) =>
                                            field.onChange(option?.value || '')
                                        }
                                    />
                                )}
                            />
                        </FormItem>
                        <FormItem label={t('nav.shared.customizeColumns')}>
                            <div className="mt-4">
                                <Controller
                                    name="visibleColumns"
                                    control={control}
                                    render={({ field }) => (
                                        <Checkbox.Group
                                            vertical
                                            className="flex"
                                            {...field}
                                        >
                                            {availableColumns.map((column) => (
                                                <Checkbox
                                                    key={column.value}
                                                    name={field.name}
                                                    value={column.value}
                                                    className="justify-between flex-row-reverse heading-text"
                                                >
                                                    {column.label}
                                                </Checkbox>
                                            ))}
                                        </Checkbox.Group>
                                    )}
                                />
                            </div>
                        </FormItem>
                    </div>
                    <Button variant="solid" type="submit">
                        {t('nav.shared.confirm')}
                    </Button>
                </Form>
            </Drawer>
        </>
    )
}

export default BuildingTableFilter
