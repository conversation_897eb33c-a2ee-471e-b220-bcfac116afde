import { useCallback, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { AppDispatch, RootState } from '@/store/store';
import {
    fetchStocks,
    fetchStockById,
    createStockAction,
    updateStockAction,
    updateStockStatusAction,
    deleteStockAction,
    clearStock,
} from '@/store/slices/stockSlice';
import {
    selectStocks,
    selectStock,
    selectLoading,
    selectError,
    selectStocksWithLanguage,
    selectStockWithLanguage,
    selectStocksCount,
    selectActiveStocks,
    selectInactiveStocks,
    selectUniqueGovernorates,
    selectUniqueCities,
    selectUniqueVillages,
    selectUniqueManagers,
    selectStocksProcessed,
    selectStockColumns,
    selectStocksStats,
    selectStocksPaginated,
} from '@/store/selectors/stockSelector';
import type { StockRequest, StockColumns } from '@/@types/stocks';

export interface StockFilters {
    status?: string;
    governorate?: string;
    city?: string;
    manager?: string;
}

export interface SortConfig {
    field: keyof StockColumns;
    direction: 'asc' | 'desc';
}

export interface PaginationConfig {
    page: number;
    pageSize: number;
}

export interface StockConfig {
    searchTerm?: string;
    filters?: StockFilters;
    sortConfig?: SortConfig;
    pagination?: PaginationConfig;
}

export const useStock = () => {
    const dispatch = useDispatch<AppDispatch>();
    
    // Local state for UI controls
    const [searchTerm, setSearchTerm] = useState<string>('');
    const [filters, setFilters] = useState<StockFilters>({});
    const [sortConfig, setSortConfig] = useState<SortConfig>({ field: 'id', direction: 'asc' });
    const [pagination, setPagination] = useState<PaginationConfig>({ page: 1, pageSize: 10 });
    const [visibleColumns, setVisibleColumns] = useState<Set<string>>(new Set(['id', 'name', 'villageNameAr', 'cityOrDistrictNameAr', 'governorateNameAr', 'managerName', 'status']));
    
    // Selection state
    const [selectedStocks, setSelectedStocks] = useState<StockColumns[]>([]);

    // Selectors
    const stocks = useSelector(selectStocks);
    const stock = useSelector(selectStock);
    const loading = useSelector(selectLoading);
    const error = useSelector(selectError);
    const stocksWithLanguage = useSelector(selectStocksWithLanguage);
    const stockWithLanguage = useSelector(selectStockWithLanguage);
    const stocksCount = useSelector(selectStocksCount);
    const activeStocks = useSelector(selectActiveStocks);
    const inactiveStocks = useSelector(selectInactiveStocks);
    const uniqueGovernorates = useSelector(selectUniqueGovernorates);
    const uniqueCities = useSelector(selectUniqueCities);
    const uniqueVillages = useSelector(selectUniqueVillages);
    const uniqueManagers = useSelector(selectUniqueManagers);
    const stocksStats = useSelector(selectStocksStats);
    const stockColumns = useSelector(selectStockColumns);

    // Processed data with search, filters, and sorting
    const processedStocks = useSelector((state: RootState) => 
        selectStocksProcessed(state, {
            searchTerm,
            filters,
            sortConfig
        })
    );

    // Paginated data
    const paginatedStocks = useSelector((state: RootState) => 
        selectStocksPaginated(state, pagination)
    );

    // CRUD Operations
    const fetchAllStocks = useCallback(async () => {
        try {
            await dispatch(fetchStocks()).unwrap();
        } catch (error) {
            console.error('Failed to fetch stocks:', error);
            throw error;
        }
    }, [dispatch]);

    const fetchStock = useCallback(async (id: number) => {
        try {
            await dispatch(fetchStockById(id)).unwrap();
        } catch (error) {
            console.error('Failed to fetch stock:', error);
            throw error;
        }
    }, [dispatch]);

    const createStock = useCallback(async (stockData: StockRequest) => {
        try {
            const result = await dispatch(createStockAction(stockData)).unwrap();
            return result;
        } catch (error) {
            console.error('Failed to create stock:', error);
            throw error;
        }
    }, [dispatch]);

    const updateStock = useCallback(async (id: number, stockData: StockRequest) => {
        try {
            const result = await dispatch(updateStockAction({ id, stock: stockData })).unwrap();
            return result;
        } catch (error) {
            console.error('Failed to update stock:', error);
            throw error;
        }
    }, [dispatch]);

    const updateStockStatus = useCallback(async (id: number, status: number) => {
        try {
            await dispatch(updateStockStatusAction({ id, status })).unwrap();
        } catch (error) {
            console.error('Failed to update stock status:', error);
            throw error;
        }
    }, [dispatch]);

    const deleteStock = useCallback(async (id: number) => {
        try {
            await dispatch(deleteStockAction(id)).unwrap();
        } catch (error) {
            console.error('Failed to delete stock:', error);
            throw error;
        }
    }, [dispatch]);

    const clearStockData = useCallback(() => {
        dispatch(clearStock());
    }, [dispatch]);

    // Search and Filter Functions
    const handleSearch = useCallback((term: string) => {
        setSearchTerm(term);
        setPagination(prev => ({ ...prev, page: 1 })); // Reset to first page on search
    }, []);

    const handleFilter = useCallback((newFilters: Partial<StockFilters>) => {
        setFilters(prev => ({ ...prev, ...newFilters }));
        setPagination(prev => ({ ...prev, page: 1 })); // Reset to first page on filter
    }, []);

    const clearFilters = useCallback(() => {
        setFilters({});
        setPagination(prev => ({ ...prev, page: 1 }));
    }, []);

    const clearSearch = useCallback(() => {
        setSearchTerm('');
        setPagination(prev => ({ ...prev, page: 1 }));
    }, []);

    // Sorting Functions
    const handleSort = useCallback((field: keyof StockColumns) => {
        setSortConfig(prev => ({
            field,
            direction: prev.field === field && prev.direction === 'asc' ? 'desc' : 'asc'
        }));
    }, []);

    const resetSort = useCallback(() => {
        setSortConfig({ field: 'id', direction: 'asc' });
    }, []);

    // Pagination Functions
    const goToPage = useCallback((page: number) => {
        setPagination(prev => ({ ...prev, page }));
    }, []);

    const changePageSize = useCallback((pageSize: number) => {
        setPagination({ page: 1, pageSize }); // Reset to first page when changing page size
    }, []);

    const nextPage = useCallback(() => {
        setPagination(prev => ({ ...prev, page: prev.page + 1 }));
    }, []);

    const prevPage = useCallback(() => {
        setPagination(prev => ({ ...prev, page: Math.max(1, prev.page - 1) }));
    }, []);

    // Column Management Functions
    const toggleColumnVisibility = useCallback((columnKey: string) => {
        setVisibleColumns(prev => {
            const newSet = new Set(prev);
            if (newSet.has(columnKey)) {
                newSet.delete(columnKey);
            } else {
                newSet.add(columnKey);
            }
            return newSet;
        });
    }, []);

    const showAllColumns = useCallback(() => {
        setVisibleColumns(new Set(stockColumns.map(col => col.key)));
    }, [stockColumns]);

    const hideAllColumns = useCallback(() => {
        setVisibleColumns(new Set(['id'])); // Keep only ID column
    }, []);

    const resetColumns = useCallback(() => {
        setVisibleColumns(new Set(['id', 'name', 'villageNameAr', 'cityOrDistrictNameAr', 'governorateNameAr', 'managerName', 'status']));
    }, []);

    // Get visible columns based on current language and visibility settings
    const getVisibleColumns = useMemo(() => {
        return stockColumns.filter(col => visibleColumns.has(col.key));
    }, [stockColumns, visibleColumns]);

    // Helper Functions
    const getStocksByGovernorate = useCallback((governorate: string) => {
        return stocks.filter(stock => stock.governorateNameAr === governorate);
    }, [stocks]);

    const getStocksByCity = useCallback((city: string) => {
        return stocks.filter(stock => stock.cityOrDistrictNameAr === city);
    }, [stocks]);

    const getStocksByStatus = useCallback((status: number) => {
        return stocks.filter(stock => stock.status === status);
    }, [stocks]);

    const getStocksByManager = useCallback((managerName: string) => {
        return stocks.filter(stock => 
            stock.managerName.toLowerCase().includes(managerName.toLowerCase())
        );
    }, [stocks]);

    const getStockById = useCallback((id: number) => {
        return stocks.find(stock => stock.id === id);
    }, [stocks]);

    // Export Functions
    const exportStocks = useCallback((format: 'csv' | 'json' = 'csv') => {
        const dataToExport = processedStocks;
        
        if (format === 'json') {
            const dataStr = JSON.stringify(dataToExport, null, 2);
            const dataBlob = new Blob([dataStr], { type: 'application/json' });
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `stocks_${new Date().toISOString().split('T')[0]}.json`;
            link.click();
            URL.revokeObjectURL(url);
        } else {
            // CSV export
            const headers = getVisibleColumns.map(col => col.label).join(',');
            const rows = dataToExport.map(stock => 
                getVisibleColumns.map(col => {
                    const value = stock[col.key as keyof StockColumns];
                    return typeof value === 'string' && value.includes(',') ? `"${value}"` : value;
                }).join(',')
            ).join('\n');
            
            const csv = `${headers}\n${rows}`;
            const dataBlob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
            const url = URL.createObjectURL(dataBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `stocks_${new Date().toISOString().split('T')[0]}.csv`;
            link.click();
            URL.revokeObjectURL(url);
        }
    }, [processedStocks, getVisibleColumns]);

    // Reset all filters and search
    const resetAll = useCallback(() => {
        setSearchTerm('');
        setFilters({});
        setSortConfig({ field: 'id', direction: 'asc' });
        setPagination({ page: 1, pageSize: 10 });
        resetColumns();
        setSelectedStocks([]);
    }, [resetColumns]);

    // Selection helpers
    const setSelectAllStocks = useCallback((stocks: StockColumns[]) => {
        setSelectedStocks(stocks);
    }, []);

    const handleSelect = useCallback((stock: StockColumns, checked: boolean) => {
        if (checked) {
            setSelectedStocks(prev => [...prev, stock]);
        } else {
            setSelectedStocks(prev => prev.filter(s => s.id !== stock.id));
        }
    }, []);

    const handleSelectAll = useCallback((checked: boolean, stocks: StockColumns[]) => {
        if (checked) {
            setSelectAllStocks(stocks);
        } else {
            setSelectAllStocks([]);
        }
    }, [setSelectAllStocks]);

    const clearSelection = useCallback(() => {
        setSelectedStocks([]);
    }, []);

    // Bulk operations
    const bulkDeleteStocks = useCallback(async () => {
        try {
            const deletePromises = selectedStocks.map(stock => deleteStock(stock.id));
            await Promise.all(deletePromises);
            setSelectedStocks([]);
            await fetchAllStocks();
        } catch (error) {
            console.error('Failed to bulk delete stocks:', error);
            throw error;
        }
    }, [selectedStocks, deleteStock, fetchAllStocks]);

    const bulkUpdateStatus = useCallback(async (status: number) => {
        try {
            const updatePromises = selectedStocks.map(stock => updateStockStatus(stock.id, status));
            await Promise.all(updatePromises);
            setSelectedStocks([]);
            await fetchAllStocks();
        } catch (error) {
            console.error('Failed to bulk update stock status:', error);
            throw error;
        }
    }, [selectedStocks, updateStockStatus, fetchAllStocks]);

    return {
        // Data
        stocks,
        stock,
        stocksWithLanguage,
        stockWithLanguage,
        processedStocks,
        paginatedStocks,
        stockColumns: getVisibleColumns,
        
        // State
        loading,
        error,
        searchTerm,
        filters,
        sortConfig,
        pagination,
        visibleColumns,
        
        // Counts and Stats
        stocksCount,
        activeStocks,
        inactiveStocks,
        stocksStats,
        uniqueGovernorates,
        uniqueCities,
        uniqueVillages,
        uniqueManagers,
        
        // CRUD Operations
        fetchAllStocks,
        fetchStock,
        createStock,
        updateStock,
        updateStockStatus,
        deleteStock,
        clearStockData,
        
        // Search and Filter
        handleSearch,
        handleFilter,
        clearFilters,
        clearSearch,
        
        // Sorting
        handleSort,
        resetSort,
        
        // Pagination
        goToPage,
        changePageSize,
        nextPage,
        prevPage,
        
        // Column Management
        toggleColumnVisibility,
        showAllColumns,
        hideAllColumns,
        resetColumns,
        
        // Helper Functions
        getStocksByGovernorate,
        getStocksByCity,
        getStocksByStatus,
        getStocksByManager,
        getStockById,
        
        // Export
        exportStocks,
        
        // Reset All
        resetAll,
        
        // Selection
        selectedStocks,
        setSelectAllStocks,
        handleSelect,
        handleSelectAll,
        clearSelection,
        
        // Bulk Operations
        bulkDeleteStocks,
        bulkUpdateStatus,
    };
};
