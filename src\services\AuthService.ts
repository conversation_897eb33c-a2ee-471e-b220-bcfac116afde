import ApiService from './ApiService'
import endpointConfig from '@/configs/endpoint.config'
import type {
    SignInCredential,
    SignUpCredential,
    ForgotPassword,
    ResetPassword,
    SignInResponse,
    SignUpResponse,
} from '@/@types/auth'

export async function apiSignIn(data: SignInCredential) {
    return ApiService.post<SignInResponse, SignInCredential>(
        endpointConfig.signIn,
        data
    )
}

export async function apiSignUp(data: SignUpCredential) {
    return ApiService.post<SignUpResponse, SignUpCredential>(
        endpointConfig.signUp,
        data
    )
}

export async function apiSignOut() {
    return ApiService.post(endpointConfig.signOut)
}

export async function apiForgotPassword<T>(data: ForgotPassword) {
    return ApiService.post<T, ForgotPassword>(
        endpointConfig.forgotPassword,
        data
    )
}

export async function apiResetPassword<T>(data: ResetPassword) {
    return ApiService.post<T, ResetPassword>(
        endpointConfig.resetPassword,
        data
    )
}
