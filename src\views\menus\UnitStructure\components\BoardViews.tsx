import { useMemo, useState, Fragment } from "react"
import {  Droppable } from "@hello-pangea/dnd"
import { buildTreeFromFlat, TreeNode, Ticket } from "../types"
import CardForOne from "./CardForOne"

const ParentNode = (
    node: TreeNode,
    index: number,
    collapsedSet: Set<string>,
    toggleCollapse: (id: string) => void,
    onNodeClick?: (node: TreeNode) => void,
) => {
    const hasChildren = Boolean(node.children && node.children.length > 0)
    const isCollapsed = collapsedSet.has(node.id)

    // Get level-specific colors
    const getLevelColors = (level: number) => {
        switch (level) {
            case 1:
                return 'bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700'
            case 2:
                return 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-700 hover:bg-blue-100 dark:hover:bg-blue-900/40'
            case 3:
                return 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-700 hover:bg-green-100 dark:hover:bg-green-900/40'
            case 4:
                return 'bg-purple-50 dark:bg-purple-900/20 border-purple-200 dark:border-purple-700 hover:bg-purple-100 dark:hover:bg-purple-900/40'
            default:
                return 'bg-gray-50 dark:bg-gray-800 border-gray-200 dark:border-gray-700 hover:bg-gray-100 dark:hover:bg-gray-700'
        }
    }

    return (
        <Droppable key={node.id} droppableId={node.id} type="ITEMS" >
            {(provided) => (
                <div 
                    ref={provided.innerRef} 
                    {...provided.droppableProps} 
                    className={`mb-2 `}
                >
                    <div className={` cursor-default rounded-lg ${getLevelColors(node.level || 0)} p-3 shadow-sm border transition-all duration-200`}>
                    {/* Card for one */}
                    <CardForOne node={node} hasChildren={hasChildren} isCollapsed={isCollapsed} toggleCollapse={toggleCollapse} onNodeClick={onNodeClick}/>

                        

                        {/* Children */}
                        {hasChildren && !isCollapsed && (
                            <Droppable droppableId={node.id} type="ITEMS">
                                {(dropProvided) => (
                                    <div
                                        ref={dropProvided.innerRef}
                                        {...dropProvided.droppableProps}
                                        className={`mt-4 pl-4 border-l-2 border-blue-300 dark:border-blue-600 transition-all duration-300
                                        `}
                                    >
                                        <div className="space-y-2">
                                            {node.children!.map((child, childIndex) => (
                                                <Fragment key={child.id}>
                                                    {ParentNode(child, childIndex, collapsedSet, toggleCollapse, onNodeClick)}
                                                </Fragment>
                                            ))}
                                        </div>
                                        {dropProvided.placeholder}
                                    </div>
                                )}
                            </Droppable>
                        )}
                    </div>
                    {provided.placeholder}
                </div>
            )}
        </Droppable>
    )
}

type BoardViewsProps = {
    items: Ticket[]
    onNodeClick?: (node: Ticket) => void
}

const BoardViews = ({ items, onNodeClick }: BoardViewsProps) => {
    const [collapsedIds, setCollapsedIds] = useState<Set<string>>(new Set())

    const tree = useMemo(() => buildTreeFromFlat(items || []), [items])

    const toggleCollapse = (id: string) => {
        setCollapsedIds((prev) => {
            const next = new Set(prev)
            if (next.has(id)) {
                next.delete(id)
            } else {
                next.add(id)
            }
            return next
        })
    }

    return (
        <Droppable droppableId="root-list" type="ITEMS">
            {(provided, snapshot) => (
                <div 
                    ref={provided.innerRef} 
                    {...provided.droppableProps}
                    className={`p-4 min-h-[300px] transition-all duration-300 ${
                        snapshot.isDraggingOver
                            ? 'bg-blue-50 dark:bg-blue-900/20 shadow-inner' 
                            : ''
                    }`}
                >
                    <div className="space-y-2">
                        {tree.map((node, index) => (
                            <Fragment key={node.id}>
                                {ParentNode(node, index, collapsedIds, toggleCollapse, onNodeClick)}
                            </Fragment>
                        ))}
                    </div>
                    {provided.placeholder}
                </div>
            )}
        </Droppable>
    )
}

export default BoardViews
