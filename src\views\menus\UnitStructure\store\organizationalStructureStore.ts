import { create } from 'zustand'
import { getOrganizeStructure, createOrganizeStructure } from '@/services/OrganizeStructureService'
import type { OrganizationalStructure, OrganizationalNode, CreatedOrganizationalStructure } from '@/@types/organizationalStructure'

type OrganizationalStructureState = {
    // Data
    organizationalStructure: OrganizationalStructure | null
    
    // Loading states
    isLoading: boolean
    
    // Error states
    error: string | null
    
    // Actions
    fetchOrganizationalStructure: () => Promise<void>
    refreshOrganizationalStructure: () => Promise<void>
    createNode: (nodeData: CreatedOrganizationalStructure) => Promise<void>
    
    
    // Getters
    getNodeByCode: (code: string) => OrganizationalNode | undefined
    getNodesByLevel: (level: number) => OrganizationalNode[]
    getTotalNodes: () => number
    getLevelCount: (level: 'L1' | 'L2' | 'L3' | 'L4') => number
    getAllNodes: () => OrganizationalNode[]
    getChildrenOfNode: (parentCode: string) => OrganizationalNode[]
    getParentOfNode: (childCode: string) => OrganizationalNode | undefined
}

const initialState = {
    organizationalStructure: null,
    isLoading: false,
    error: null,
}

export const useOrganizationalStructureStore = create<OrganizationalStructureState>((set, get) => ({
    ...initialState,

    // Fetch organizational structure (with caching)
    fetchOrganizationalStructure: async () => {
        const { organizationalStructure } = get()
        
        // Return cached data if already loaded
        if (organizationalStructure) {
            return
        }

        set({ isLoading: true, error: null })
        
        try {
            const data = await getOrganizeStructure()
            set({ organizationalStructure: data, isLoading: false })
        } catch (error) {
            set({ 
                error: error instanceof Error ? error.message : 'Failed to fetch organizational structure',
                isLoading: false 
            })
        }
    },

    // Refresh organizational structure (always fetch fresh data)
    refreshOrganizationalStructure: async () => {
        set({ isLoading: true, error: null })
        
        try {
            const data = await getOrganizeStructure()
            set({ organizationalStructure: data, isLoading: false })
        } catch (error) {
            set({ 
                error: error instanceof Error ? error.message : 'Failed to refresh organizational structure',
                isLoading: false 
            })
        }
    },

    // Create new node
    createNode: async (nodeData: CreatedOrganizationalStructure) => {
        set({ isLoading: true, error: null })
        
        try {
            await createOrganizeStructure(nodeData)
            // Refresh the data after creating
            await get().fetchOrganizationalStructure()
            set({ isLoading: false })
        } catch (error) {
            set({ 
                error: error instanceof Error ? error.message : 'Failed to create node',
                isLoading: false 
            })
            throw error
        }
    },

    // Get node by code - improved recursive search
    getNodeByCode: (code: string) => {
        const { organizationalStructure } = get()
        if (!organizationalStructure?.rootNodes) return undefined
        
        const findNode = (nodes: OrganizationalNode[]): OrganizationalNode | undefined => {
            for (const node of nodes) {
                if (node.code === code) return node
                
                // Search in children if they exist
                if (node.children && node.children.length > 0) {
                    const found = findNode(node.children)
                    if (found) return found
                }
            }
            return undefined
        }
        
        return findNode(organizationalStructure.rootNodes)
    },

    // Get nodes by level - improved recursive search
    getNodesByLevel: (level: number) => {
        const { organizationalStructure } = get()
        if (!organizationalStructure?.rootNodes) return []
        
        const findNodesByLevel = (nodes: OrganizationalNode[], targetLevel: number): OrganizationalNode[] => {
            let result: OrganizationalNode[] = []
            
            for (const node of nodes) {
                if (node.level === targetLevel) {
                    result.push(node)
                }
                
                // Recursively search in children
                if (node.children && node.children.length > 0) {
                    result = result.concat(findNodesByLevel(node.children, targetLevel))
                }
            }
            
            return result
        }
        
        return findNodesByLevel(organizationalStructure.rootNodes, level)
    },

    // Get all nodes in a flat array
    getAllNodes: () => {
        const { organizationalStructure } = get()
        if (!organizationalStructure?.rootNodes) return []
        
        const getAllNodesRecursive = (nodes: OrganizationalNode[]): OrganizationalNode[] => {
            let result: OrganizationalNode[] = []
            
            for (const node of nodes) {
                result.push(node)
                
                if (node.children && node.children.length > 0) {
                    result = result.concat(getAllNodesRecursive(node.children))
                }
            }
            
            return result
        }
        
        return getAllNodesRecursive(organizationalStructure.rootNodes)
    },

    // Get children of a specific node
    getChildrenOfNode: (parentCode: string) => {
        const { organizationalStructure } = get()
        if (!organizationalStructure?.rootNodes) return []
        
        const parentNode = get().getNodeByCode(parentCode)
        return parentNode?.children || []
    },

    // Get parent of a specific node
    getParentOfNode: (childCode: string) => {
        const { organizationalStructure } = get()
        if (!organizationalStructure?.rootNodes) return undefined
        
        const findParent = (nodes: OrganizationalNode[], targetCode: string): OrganizationalNode | undefined => {
            for (const node of nodes) {
                if (node.children && node.children.some(child => child.code === targetCode)) {
                    return node
                }
                
                if (node.children && node.children.length > 0) {
                    const found = findParent(node.children, targetCode)
                    if (found) return found
                }
            }
            return undefined
        }
        
        return findParent(organizationalStructure.rootNodes, childCode)
    },

    // Get total nodes
    getTotalNodes: () => {
        const { organizationalStructure } = get()
        return organizationalStructure?.totalNodes || get().getAllNodes().length
    },

    // Get level count
    getLevelCount: (level: 'L1' | 'L2' | 'L3' | 'L4') => {
        const { organizationalStructure } = get()
        if (organizationalStructure?.levelCounts) {
            return organizationalStructure.levelCounts[level] || 0
        }
        
        // Fallback: calculate from actual data
        const levelNumber = parseInt(level.substring(1))
        return get().getNodesByLevel(levelNumber).length
    },
}))
