import {
    PiPuzzlePieceDuotone,
    PiSwatchesDuotone,
    PiChatDotsDuotone,
    PiDesktopDuotone,
    PiFileTextDuotone,
    PiCompassDuotone,
    PiChartPieSliceDuotone,
    PiUsersDuotone,
    PiPackageDuotone,
    PiProjectorScreenChartDuotone,
    PiShoppingCartDuotone,
    PiCalendarDuotone,
    PiFolderOpenDuotone,
    PiEnvelopeDuotone,
    PiUserCircleDuotone,
    PiSparkleDuotone,
    PiChatCircleDuotone,
    PiQuestionDuotone,
    PiShieldCheckDuotone,
    PiUserPlusDuotone,
    PiLockKeyOpenDuotone,
    PiKeyDuotone,
    PiShoppingCartSimpleDuotone,
    PiRocketDuotone,
    PiChartBarDuotone,
    PiMegaphoneDuotone,
    PiSpeedometerDuotone,
    PiLightbulbDuotone,
    PiKeyholeDuotone,
    PiChatCenteredDotsDuotone,
    PiImagesDuotone,
    PiChalkboardSimpleDuotone,
    PiListChecksDuotone,
    PiUserListDuotone,
    PiListStarDuotone,
    PiFileMagnifyingGlassDuotone,
    PiCalendarCheckDuotone,
    PiWarningDuotone,
    PiPencilSimpleLineDuotone,
    PiNotePencilDuotone,
    PiPlusSquareDuotone,
    PiListPlusDuotone,
    PiFilmScriptDuotone,
    PiStackPlusDuotone,
    PiListMagnifyingGlassDuotone,
    PiGearDuotone,
    PiUserGearDuotone,
    PiCurrencyCircleDollarDuotone,
    PiNewspaperDuotone,
    PiHeadsetDuotone,
    PiQuotesDuotone,
    PiTextIndentDuotone,
    PiCursorClickDuotone,
    PiGridFourDuotone,
    PiTextAaDuotone,
    PiHandWithdrawDuotone,
    PiSpinnerGapDuotone,
    PiBoneDuotone,
    PiSpinnerBallDuotone,
    PiBreadDuotone,
    PiMedalDuotone,
    PiCardsDuotone,
    PiTableDuotone,
    PiTagDuotone,
    PiClockCountdownDuotone,
    PiChatCenteredDuotone,
    PiCheckSquareDuotone,
    PiClipboardTextDuotone,
    PiRowsDuotone,
    PiRowsPlusBottomDuotone,
    PiRadioButtonDuotone,
    PiChartDonutDuotone,
    PiListChecksBold,
    PiToggleRightDuotone,
    PiClockAfternoonDuotone,
    PiUploadDuotone,
    PiCaretCircleDownDuotone,
    PiListDuotone,
    PiCodeSimpleDuotone,
    PiFootprintsDuotone,
    PiBrowsersDuotone,
    PiChartLineUpDuotone,
    PiMapTrifoldDuotone,
    PiBookDuotone,
    PiShareNetworkDuotone,
    PiToolboxDuotone,
    PiCodeDuotone,
    PiPasswordDuotone,
} from 'react-icons/pi'
import type { JSX } from 'react'

export type NavigationIcons = Record<string, JSX.Element>

const navigationIcon: NavigationIcons = {
    menus: <PiListDuotone />,
    uiComponents: <PiPuzzlePieceDuotone />,
    common: <PiSwatchesDuotone />,
    uiCommonButton: <PiCursorClickDuotone />,
    uiCommonGrid: <PiGridFourDuotone />,
    uiCommonTypography: <PiTextAaDuotone />,
    uiCommonIcons: <PiImagesDuotone />,
    feedback: <PiChatDotsDuotone />,
    uiFeedbackAlert: <PiWarningDuotone />,
    uiFeedbackDialog: <PiChatCircleDuotone />,
    uiFeedbackDrawer: <PiHandWithdrawDuotone />,
    uiFeedbackProgress: <PiSpinnerGapDuotone />,
    uiFeedbackSkeleton: <PiBoneDuotone />,
    uiFeedbackSpinner: <PiSpinnerBallDuotone />,
    uiFeedbackToast: <PiBreadDuotone />,
    dataDisplay: <PiDesktopDuotone />,
    uiDataDisplayAvatar: <PiUserCircleDuotone />,
    uiDataDisplayBadge: <PiMedalDuotone />,
    uiDataDisplayCalendar: <PiCalendarDuotone />,
    uiDataDisplayCard: <PiCardsDuotone />,
    uiDataDisplayTable: <PiTableDuotone />,
    uiDataDisplayTag: <PiTagDuotone />,
    uiDataDisplayTimeline: <PiClockCountdownDuotone />,
    uiDataDisplayTooltip: <PiChatCenteredDuotone />,
    forms: <PiFileTextDuotone />,
    uiFormsCheckbox: <PiCheckSquareDuotone />,
    uiFormsDatepicker: <PiCalendarCheckDuotone />,
    uiFormsFormControl: <PiClipboardTextDuotone />,
    uiFormsInput: <PiRowsDuotone />,
    uiFormsInputGroup: <PiRowsPlusBottomDuotone />,
    uiFormsRadio: <PiRadioButtonDuotone />,
    uiFormsSegment: <PiChartDonutDuotone />,
    uiFormsSelect: <PiListChecksBold />,
    uiFormsSwitcher: <PiToggleRightDuotone />,
    uiFormsTimePicker: <PiClockAfternoonDuotone />,
    uiFormsUpload: <PiUploadDuotone />,
    navigation: <PiCompassDuotone />,
    uiNavigationDropdown: <PiCaretCircleDownDuotone />,
    uiNavigationMenu: <PiListDuotone />,
    uiNavigationPagination: <PiCodeSimpleDuotone />,
    uiNavigationSteps: <PiFootprintsDuotone />,
    uiNavigationTabs: <PiBrowsersDuotone />,
    graph: <PiChartPieSliceDuotone />,
    uiGraphChart: <PiChartLineUpDuotone />,
    uiGraphMaps: <PiMapTrifoldDuotone />,
    concepts: <PiLightbulbDuotone />,
    customers: <PiUsersDuotone />,
    customerList: <PiUserListDuotone />,
    customerEdit: <PiPencilSimpleLineDuotone />,
    customerCreate: <PiUserPlusDuotone />,
    customerDetails: <PiUserCircleDuotone />,
    products: <PiPackageDuotone />,
    productList: <PiListStarDuotone />,
    productEdit: <PiNotePencilDuotone />,
    productCreate: <PiPlusSquareDuotone />,
    projects: <PiProjectorScreenChartDuotone />,
    projectScrumBoard: <PiChalkboardSimpleDuotone />,
    projectList: <PiListChecksDuotone />,
    projectDetails: <PiFileMagnifyingGlassDuotone />,
    projectTask: <PiCalendarCheckDuotone />,
    projectIssue: <PiWarningDuotone />,
    orders: <PiShoppingCartDuotone />,
    orderList: <PiListPlusDuotone />,
    orderEdit: <PiFilmScriptDuotone />,
    ordeDetails: <PiListMagnifyingGlassDuotone />,
    orderCreate: <PiStackPlusDuotone />,
    calendar: <PiCalendarDuotone />,
    fileManager: <PiFolderOpenDuotone />,
    mail: <PiEnvelopeDuotone />,
    account: <PiUserCircleDuotone />,
    accountSettings: <PiGearDuotone />,
    accountActivityLogs: <PiFileTextDuotone />,
    accountRoleAndPermission: <PiUserGearDuotone />,
    accountPricing: <PiCurrencyCircleDollarDuotone />,
    ai: <PiSparkleDuotone />,
    aiChat: <PiChatCenteredDotsDuotone />,
    aiImage: <PiImagesDuotone />,
    chat: <PiChatCircleDuotone />,
    helpCenter: <PiQuestionDuotone />,
    helpCeterSupportHub: <PiHeadsetDuotone />,
    helpCeterArticle: <PiNewspaperDuotone />,
    helpCeterEditArticle: <PiTextIndentDuotone />,
    helpCeterManageArticle: <PiQuotesDuotone />,
    authentication: <PiShieldCheckDuotone />,
    signIn: <PiKeyDuotone />,
    signUp: <PiUserPlusDuotone />,
    forgotPassword: <PiLockKeyOpenDuotone />,
    resetPassword: <PiKeyholeDuotone />,
    otpVerification: <PiPasswordDuotone />,
    dashboard: <PiSpeedometerDuotone />,
    dashboardEcommerce: <PiShoppingCartSimpleDuotone />,
    dashboardProject: <PiRocketDuotone />,
    dashboardAnalytic: <PiChartBarDuotone />,
    dashboardMarketing: <PiMegaphoneDuotone />,
    documentation: <PiBookDuotone />,
    sharedComponentDoc: <PiShareNetworkDuotone />,
    utilsDoc: <PiToolboxDuotone />,
    changeLog: <PiCodeDuotone />,
}

export default navigationIcon
