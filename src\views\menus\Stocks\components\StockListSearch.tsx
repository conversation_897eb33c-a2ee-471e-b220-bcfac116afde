import { useStock } from '@/utils/hooks/useStock'
import Input from '@/components/ui/Input'
import useTranslation from '@/utils/hooks/useTranslation'
import { TbSearch, TbX } from 'react-icons/tb'

const StockListSearch = () => {
    const { t } = useTranslation()
    const { searchTerm, handleSearch, clearSearch } = useStock()

    return (
        <div className="flex items-center gap-2">
            <div className="relative">
                <Input
                    placeholder={t('nav.shared.search')}
                    value={searchTerm}
                    suffix={<TbSearch className="text-lg" />}
                    className="w-64"
                    onChange={(e) => handleSearch(e.target.value)}
                />
                {searchTerm && (
                    <button
                    className="absolute right-2 top-1/2 -translate-y-1/2 text-gray-400 hover:text-gray-600"
                    onClick={clearSearch}
                    >
                        <TbX className="text-lg" />
                    </button>
                )}
            </div>
        </div>
    )
}

export default StockListSearch
