import { createAsyncThunk, createSlice } from '@reduxjs/toolkit'
import {
    Building,
    createBuilding,
    getBuildingById,
    getBuildings,
    updateBuilding,
    deleteBuilding,
} from '@/services/BuildingService'

export const fetchBuildings = createAsyncThunk(
    'building/fetchBuildings',
    async () => {
        return await getBuildings()
    },
)

export const fetchBuildingById = createAsyncThunk(
    'building/fetchBuildingById',
    async (id: string) => {
        return await getBuildingById(id)
    },
)

export const createBuildingAction = createAsyncThunk(
    'building/createBuilding',
    async (building: Building) => {
        return await createBuilding(building)
    },
)

export const updateBuildingAction = createAsyncThunk(
    'building/updateBuilding',
    async (building: Building) => {
        return await updateBuilding(building.id.toString(), building)
    },
)

export const deleteBuildingAction = createAsyncThunk(
    'building/deleteBuilding',
    async (id: string) => {
        return await deleteBuilding(id)
    },
)

const initialState = {
    buildings: [] as Building[],
    building: null as Building | null,
    loading: false,
    error: false,
}

export const buildingSlice = createSlice({
    name: 'building',
    initialState,
    reducers: {
        clearBuilding: (state) => {
            state.building = null
        },
    },
    extraReducers: (builder) => {
        builder
            .addCase(fetchBuildings.pending, (state) => {
                state.loading = true
                state.error = false
            })
            .addCase(fetchBuildings.fulfilled, (state, action) => {
                state.buildings = action.payload
                state.loading = false
                state.error = false
            })
            .addCase(fetchBuildings.rejected, (state) => {
                state.loading = false
                state.error = true
            })
            .addCase(fetchBuildingById.pending, (state) => {
                state.loading = true
                state.error = false
            })
            .addCase(fetchBuildingById.fulfilled, (state, action) => {
                state.building = action.payload
                state.loading = false
                state.error = false
            })
            .addCase(fetchBuildingById.rejected, (state) => {
                state.loading = false
                state.error = true
            })
            .addCase(createBuildingAction.pending, (state) => {
                state.loading = true
                state.error = false
            })
            .addCase(createBuildingAction.fulfilled, (state, action) => {
                state.buildings.push(action.payload)
                state.loading = false
                state.error = false
            })
            .addCase(createBuildingAction.rejected, (state) => {
                state.loading = false
                state.error = true
            })
            .addCase(updateBuildingAction.pending, (state) => {
                state.loading = true
                state.error = false
            })
            .addCase(updateBuildingAction.fulfilled, (state, action) => {
                state.buildings = state.buildings.map((building) =>
                    building.id === action.payload.id
                        ? action.payload
                        : building,
                )
                state.loading = false
                state.error = false
            })
            .addCase(updateBuildingAction.rejected, (state) => {
                state.loading = false
                state.error = true
            })
            .addCase(deleteBuildingAction.pending, (state) => {
                state.loading = true
                state.error = false
            })
            .addCase(deleteBuildingAction.fulfilled, (state, action) => {
                state.buildings = state.buildings.filter(
                    (building) => building.id.toString() !== action.meta.arg,
                )
                state.loading = false
                state.error = false
            })
            .addCase(deleteBuildingAction.rejected, (state) => {
                state.loading = false
                state.error = true
            })
    },
})

export const buildingReducer = buildingSlice.reducer
