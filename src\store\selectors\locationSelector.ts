import { RootState } from '../store'
import type { Country, Governorate, City, District } from '@/@types/location'

const selectLocationState = (state: RootState) => state.location

// Basic selectors
export const selectCountries = (state: RootState) =>
    selectLocationState(state).countries
export const selectGovernorates = (state: RootState) =>
    selectLocationState(state).governorates
export const selectCities = (state: RootState) =>
    selectLocationState(state).cities
export const selectDistricts = (state: RootState) =>
    selectLocationState(state).districts

// Loading selectors
export const selectLoadingCountries = (state: RootState) =>
    selectLocationState(state).isLoadingCountries
export const selectLoadingGovernorates = (state: RootState) =>
    selectLocationState(state).isLoadingGovernorates
export const selectLoadingCities = (state: RootState) =>
    selectLocationState(state).isLoadingCities
export const selectLoadingDistricts = (state: RootState) =>
    selectLocationState(state).isLoadingDistricts

// Error selectors
export const selectCountriesError = (state: RootState) =>
    selectLocationState(state).countriesError
export const selectGovernoratesError = (state: RootState) =>
    selectLocationState(state).governoratesError
export const selectCitiesError = (state: RootState) =>
    selectLocationState(state).citiesError
export const selectDistrictsError = (state: RootState) =>
    selectLocationState(state).districtsError

// Utility functions for getting location data by code
export const getCountryByCode = (
    countries: Country[],
    code: string,
): Country | undefined => {
    return countries.find((country) => country.code === code)
}

export const getGovernorateByCode = (
    governorates: Governorate[],
    code: string,
): Governorate | undefined => {
    return governorates.find((governorate) => governorate.code === code)
}

export const getCityByCode = (
    cities: City[],
    code: string,
): City | undefined => {
    return cities.find((city) => city.code === code)
}

export const getDistrictByCode = (
    districts: District[],
    code: string,
): District | undefined => {
    return districts.find((district) => district.code === code)
}

// Convert location array to select options
export const locationToSelectOptions = (
    locations: (Country | Governorate | City | District)[],
) => {
    return locations.map((location) => ({
        value: location.code,
        label: location.nameAr || location.nameEn || location.code,
    }))
}

// Memoized selectors for getting data by code
export const selectCountryByCode = (state: RootState, code: string) => {
    const countries = selectCountries(state)
    return getCountryByCode(countries, code)
}

export const selectGovernorateByCode = (state: RootState, code: string) => {
    const governorates = selectGovernorates(state)
    return getGovernorateByCode(governorates, code)
}

export const selectCityByCode = (state: RootState, code: string) => {
    const cities = selectCities(state)
    return getCityByCode(cities, code)
}

export const selectDistrictByCode = (state: RootState, code: string) => {
    const districts = selectDistricts(state)
    return getDistrictByCode(districts, code)
}

// Convert location data to select options
export const selectCountriesAsOptions = (state: RootState) => {
    const countries = selectCountries(state)
    return locationToSelectOptions(countries)
}

export const selectGovernoratesAsOptions = (state: RootState) => {
    const governorates = selectGovernorates(state)
    return locationToSelectOptions(governorates)
}

export const selectCitiesAsOptions = (state: RootState) => {
    const cities = selectCities(state)
    return locationToSelectOptions(cities)
}

export const selectDistrictsAsOptions = (state: RootState) => {
    const districts = selectDistricts(state)
    return locationToSelectOptions(districts)
}

// Get location hierarchy
export const selectLocationHierarchy = (
    state: RootState,
    countryCode?: string,
    governorateCode?: string,
    cityCode?: string,
    districtCode?: string,
) => {
    const countries = selectCountries(state)
    const governorates = selectGovernorates(state)
    const cities = selectCities(state)
    const districts = selectDistricts(state)

    return {
        country: countryCode
            ? getCountryByCode(countries, countryCode)
            : undefined,
        governorate: governorateCode
            ? getGovernorateByCode(governorates, governorateCode)
            : undefined,
        city: cityCode ? getCityByCode(cities, cityCode) : undefined,
        district: districtCode
            ? getDistrictByCode(districts, districtCode)
            : undefined,
    }
}
