import Card from '@/components/ui/Card'
import Input from '@/components/ui/Input'
import Select from '@/components/ui/Select'
import { FormItem } from '@/components/ui/Form'
import NumericInput from '@/components/shared/NumericInput'
import { Controller, useWatch } from 'react-hook-form'
import type { FormSectionBaseProps, CountRange } from '../types'
import { TbBox, TbLink, TbPlus, TbShoppingBag } from 'react-icons/tb'
import classNames from '@/utils/classNames'
import { useTranslation } from 'react-i18next'
import Tooltip from '@/components/ui/Tooltip'

type CategoryOption = {
    value: string
    label: string
    icon: React.ReactNode
}
type BasicInfoSectionProps = FormSectionBaseProps

const statusOptions = [
    { value: 'معتمد', label: 'معتمد' },
    { value: 'غير معتمد', label: 'غير معتمد' },
]

const warehouseOptions = [
    { value: 'المخزن الرئيسي', label: 'المخزن الرئيسي' },
    { value: 'فرع القاهرة', label: 'فرع القاهرة' },
    { value: 'فرع الإسكندرية', label: 'فرع الإسكندرية' },
    { value: 'فرع طنطا', label: 'فرع طنطا' },
    { value: 'فرع المنصورة', label: 'فرع المنصورة' },
]


const categoryOptions: CategoryOption[] = [
    {
        value: 'صناديق',
        label: 'صناديق',
        icon: <TbBox className="text-3xl" />,
    },
    {
        value: 'أكياس',
        label: 'أكياس',
        icon: <TbShoppingBag className="text-3xl" />,
    },
    {
        value: 'أفيز',
        label: 'أفيز',
        icon: <TbLink className="text-3xl" />,
    },
    {
        value: '',
        label: 'اضافة',
        icon: <TbPlus className="text-3xl" />,
    },
]

const BasicInfoSection = ({ control, errors }: BasicInfoSectionProps) => {
    const { t } = useTranslation()
    
    // Watch the category value to show/hide count range
    const selectedCategory = useWatch({
        control,
        name: "category"
    })

    return (
        <Card className="w-full p-0">

            <FormItem
                className='mb-4'
                invalid={Boolean(errors.category)}
                errorMessage={errors.category?.message}
            >
                <Controller
                    name="category"
                    control={control}
                    render={({ field }) => (
                        <div className="grid grid-cols-2 sm:grid-cols-4 md:grid-cols-6 gap-4">
                            {categoryOptions.map((option) => (
                                <Tooltip
                                    key={option.value}
                                    title={option.label}
                                    placement="top"
                                >
                                    <div
                                        className={classNames(
                                            'border-2 rounded-xl p-1 cursor-pointer transition-all duration-300 hover:shadow-lg hover:scale-105',
                                            field.value === option.value
                                                ? 'border-primary bg-primary/5 shadow-lg scale-105'
                                                : 'border-gray-200 dark:border-gray-600 hover:border-primary/50',
                                        )}
                                        onClick={() => field.onChange(option.value)}
                                    >
                                        <div className="flex flex-col items-center justify-center h-20">
                                            <div
                                                className={classNames(
                                                    'p-3 rounded-full transition-all duration-300',
                                                    field.value === option.value
                                                        ? 'text-primary bg-primary/10 scale-110 shadow-md'
                                                        : 'text-gray-500 bg-gray-100 dark:bg-gray-700 hover:scale-105 hover:shadow-md',
                                                )}
                                            >
                                                {option.icon}
                                            </div>
                                            {field.value === option.value && (
                                                <div className="w-2 h-2 bg-primary rounded-full mt-2 animate-pulse"></div>
                                            )}
                                        </div>
                                    </div>
                                </Tooltip>
                            ))}
                        </div>
                    )}
                />
            </FormItem>

            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormItem
                    className='mb-4'
                    label={t('nav.consumables.type')}
                    invalid={Boolean(errors.category)}
                    errorMessage={errors.category?.message}
                >
                    <Controller
                        name="category"
                        control={control}
                        render={({ field }) => (
                            <Input
                                type="text"
                                autoComplete="off"
                                placeholder={t('nav.consumables.type')}
                                {...field}
                            />
                        )}
                    />
                </FormItem>

                <FormItem
                    className='mb-4'
                    label={selectedCategory === 'أفيز' ? t('nav.consumables.countRange') : t('nav.consumables.count')}
                    invalid={Boolean(errors.count)}
                    errorMessage={errors.count?.message}
                >
                    <Controller
                        name="count"
                        control={control}
                        render={({ field }) => (
                            selectedCategory === 'أفيز' ? (
                                <div className="flex items-center space-x-2">
                                    <NumericInput
                                        type="text"
                                        autoComplete="off"
                                        placeholder="من"
                                        className="flex-1"
                                        value={typeof field.value === 'object' && field.value ? (field.value as CountRange).from || '' : ''}
                                        onChange={(value) => {
                                            const currentValue = typeof field.value === 'object' && field.value ? field.value as CountRange : {} as CountRange
                                            field.onChange({ ...currentValue, from: value })
                                        }}
                                    />
                                    <span className="text-gray-500">إلى</span>
                                    <NumericInput
                                        type="text"
                                        autoComplete="off"
                                        placeholder="إلى"
                                        className="flex-1"
                                        value={typeof field.value === 'object' && field.value ? (field.value as CountRange).to || '' : ''}
                                        onChange={(value) => {
                                            const currentValue = typeof field.value === 'object' && field.value ? field.value as CountRange : {} as CountRange
                                            field.onChange({ ...currentValue, to: value })
                                        }}
                                    />
                                </div>
                            ) : (
                                <NumericInput
                                    type="text"
                                    autoComplete="off"
                                    placeholder={t('nav.consumables.enterCount')}
                                    value={typeof field.value === 'object' ? '' : field.value}
                                    onChange={field.onChange}
                                />
                            )
                        )}
                    />
                </FormItem>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 ">
                <FormItem
                    className='mb-4'
                    label={t('nav.consumables.warehouse')}
                    invalid={Boolean(errors.wareHouse)}
                    errorMessage={errors.wareHouse?.message}
                >
                    <Controller
                        name="wareHouse"
                        control={control}
                        render={({ field }) => (
                            <Select
                                options={warehouseOptions}
                                placeholder={t('nav.consumables.selectWarehouse')}
                                value={warehouseOptions.filter(
                                    (option) => option.value === field.value,
                                )}
                                onChange={(option) =>
                                    field.onChange(option?.value || '')
                                }
                            />
                        )}
                    />
                </FormItem>

                <FormItem
                    className='mb-4'
                    label={t('nav.shared.status')}
                    invalid={Boolean(errors.status)}
                    errorMessage={errors.status?.message}
                >
                    <Controller
                        name="status"
                        control={control}
                        render={({ field }) => (
                            <Select
                                options={statusOptions}
                                placeholder={t('nav.shared.selectStatus')}
                                value={statusOptions.filter(
                                    (option) => option.value === field.value,
                                )}
                                onChange={(option) =>
                                    field.onChange(option?.value || '')
                                }
                            />
                        )}
                    />
                </FormItem>
            </div>



            <div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
            <FormItem
                    className='mb-4'
                    label={t('nav.shared.description')}
                    invalid={Boolean(errors.description)}
                    errorMessage={errors.description?.message}
                >
                    <Controller
                        name="description"
                        control={control}
                        render={({ field }) => (
                            <Input
                                type="text"
                                autoComplete="off"
                                placeholder={t('nav.shared.enterDescription')}
                                {...field}
                            />
                        )}
                    />
                </FormItem>

                
            <FormItem
                className='mb-4'
                label={t('nav.consumables.attachments')}
                invalid={Boolean(errors.attachments)}
                errorMessage={errors.attachments?.message}
                >
                <Controller
                    name="attachments"
                    control={control}
                    render={({ field }) => (
                        <div className="space-y-2">
                            <Input
                                type="file"
                                accept="image/*,.pdf,.doc,.docx"
                                onChange={(e) => {
                                    const file = e.target.files?.[0]
                                    if (file) {
                                        field.onChange(file.name)
                                    }
                                }}
                            />
                            {field.value && (
                                <div className="text-sm text-gray-600 dark:text-gray-400">
                                    {t('nav.shared.selected')}: {field.value}
                                </div>
                            )}
                        </div>
                    )}
                />
            </FormItem>
                                    </div>
        </Card>
    )
}

export default BasicInfoSection
