import { useState } from 'react'
import { useOrganizationalStructure } from './hook/useOrganizationalStructure'
import { createOrganizeStructure, deleteOrganizeStructure } from '@/services/OrganizeStructureService'
import AdaptiveCard from '@/components/shared/AdaptiveCard'
import ScrumBoardHeader from './components/ScrumBoardHeader'
import BoardViews from './components/BoardViews'
import NodeDialog from './components/NodeDialog'
import type { NewItem,  Ticket } from './types'
import { flattenOrganizationalStructure } from './types'
import { DragDropContext, type DropResult } from '@hello-pangea/dnd'

const ScrumBoard = () => {
    const { organizationalStructure, refresh } = useOrganizationalStructure(true)
    const [newItems, setNewItems] = useState<NewItem[]>([])
    const [isDialogOpen, setIsDialogOpen] = useState(false)
    const [selectedNode, setSelectedNode] = useState<Ticket | null>(null)
    const [localItems, setLocalItems] = useState<Ticket[]>([])
    const [pendingItems, setPendingItems] = useState<Ticket[]>([])

    const serverItems: Ticket[] = organizationalStructure?.rootNodes
        ? flattenOrganizationalStructure(organizationalStructure.rootNodes)
        : []

    // Use local items if available, otherwise use server items
    const items = localItems.length > 0 ? localItems : serverItems

    const onDragEnd = (result: DropResult) => {
        const { destination, draggableId } = result
        console.log(result)
        if (!destination) return console.log('no destination')

        // Handle new item drop
        if (draggableId.startsWith('pending-item-')) {
            const parentCode = destination.droppableId === 'root-list' ? null : destination.droppableId
            const itemIndex = parseInt(draggableId.replace('pending-item-', ''))
            const pendingItem = pendingItems[itemIndex]
            
            if (!pendingItem) return
            
            // Create the new organizational node
            const newNode = {
                name: pendingItem.name,
                description: pendingItem.description || '',
                parentCode: parentCode || ''
            }

            console.log(newNode)
            createOrganizeStructure(newNode)
                .then(async (response) => {
                    // Create a temporary local item to show immediately
                    const tempItem: Ticket = {
                        id: response.rootNodes?.[0]?.code || `temp-${Date.now()}`,
                        name: newNode.name,
                        description: newNode.description,
                        level: 1, // Default level
                        parentId: newNode.parentCode || null,
                        childrenCount: 0
                    }
                    
                    // Add to local items immediately
                    setLocalItems(prev => [...prev, tempItem])
                    
                    setNewItems([{ name: '', description: '', parentCode: '' }])
                    // Remove the dropped item from pending items
                    setPendingItems(prev => prev.filter((_, index) => index !== itemIndex))
                    
                    // Refresh from server to get the real data
                    await refresh()
                    
                    // Clear local items after server refresh
                    setLocalItems([])
                })
                .catch((error: Error) => {
                    console.log(error)
                })
        }
    }

    const handleNodeClick = (node: Ticket) => {
        setSelectedNode(node)
        setIsDialogOpen(true)
    }

    const handleAddItem = () => {
        if (!newItems[0].name.trim()) return
        
        // Create a pending item that will be added to the droppable area
        const tempItem: Ticket = {
            id: `pending-${Date.now()}`,
            name: newItems[0].name,
            description: newItems[0].description,
            level: 1,
            parentId: null,
            childrenCount: 0
        }
        
        setPendingItems(prev => [...prev, tempItem])
        setNewItems([{ name: '', description: '', parentCode: '' }])
    }

    const handleDeleteNode = async (nodeId: string) => {
        try {
            await deleteOrganizeStructure(nodeId)
        } catch (error) {
            console.error('Error deleting node:', error)
            throw error
        }
    }

    return (
        <>
            <DragDropContext onDragEnd={onDragEnd}>
                <AdaptiveCard className="h-full" bodyClass="h-full flex flex-col">
                    <ScrumBoardHeader 
                        newItems={newItems} 
                        setNewItems={setNewItems} 
                        onAddItem={handleAddItem}
                        pendingItems={pendingItems}
                        setPendingItems={setPendingItems}
                    />
                    <div className="flex-1 overflow-auto">
                        <BoardViews items={items} onNodeClick={handleNodeClick} />
                    </div>
                </AdaptiveCard>
            </DragDropContext>
            
            {/* Node Dialog for editing/deleting */}
            <NodeDialog
                isOpen={isDialogOpen}
                node={selectedNode}
                onDelete={handleDeleteNode}
                onRefresh={refresh}
                onClose={() => {
                    setIsDialogOpen(false)
                    setSelectedNode(null)
                }}
            />
        </>
    )
}

export default ScrumBoard
