export interface OrganizationalNode {
    code: string
    name: string
    description: string
    level: number
    children?: OrganizationalNode[]
    childrenCount?: number
    parentCode?:string
}

export interface LevelCounts {
    L1: number
    L2: number
    L3: number
    L4: number
}

export interface OrganizationalStructure {
    rootNodes: OrganizationalNode[]
    levelCounts?: LevelCounts
    totalNodes?: number
    parent?: string
    parentCode?: string
}

export interface CreatedOrganizationalStructure {
    name: string
    description?: string
    parentCode: string
}