import { useState, useEffect, useMemo } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { AppDispatch } from '../../../../store/store'
import { fetchBuildings } from '../../../../store/slices/buildingSlice'
import { Building } from '../../../../services/BuildingService'
import {
    selectBuildings,
    selectLoading,
    selectError,
    selectBuildingsWithLanguage,
} from '../../../../store/selectors/buildingSelector'
import { useLocaleStore } from '../../../../store/localeStore'
import type { TableQueries } from '@/@types/common'

export interface BuildingListState {
    list: Building[]
    total: number
}

// Enhanced Building interface with display fields
export interface BuildingWithDisplay extends Building {
    villageName: string
    cityName: string
    governorateName: string
    countryName: string
    displayName: string
    displayVillage: string
    displayCity: string
    displayGovernorate: string
    displayCountry: string
}

const useBuildingList = () => {
    const dispatch = useDispatch<AppDispatch>()
    const buildings = useSelector(selectBuildings)
    const buildingsWithLanguage = useSelector(
        selectBuildingsWithLanguage,
    ) as BuildingWithDisplay[]
    const loading = useSelector(selectLoading)
    const error = useSelector(selectError)
    const { currentLang } = useLocaleStore()

    // Local state for table management
    const [tableData, setTableData] = useState<TableQueries>({
        pageIndex: 1,
        pageSize: 10,
        total: 0,
        query: '',
    })
    const [filterData, setFilterData] = useState<TableQueries>({
        pageIndex: 1,
        pageSize: 10,
        total: 0,
        query: '',
    })
    const [selectedBuilding, setSelectedBuilding] = useState<
        BuildingWithDisplay[]
    >([])
    const [searchTerm, setSearchTerm] = useState('')
    const [statusFilter, setStatusFilter] = useState('')
    const [governorateFilter, setGovernorateFilter] = useState('')
    const [cityFilter, setCityFilter] = useState('')

    // Fetch buildings on component mount
    useEffect(() => {
        dispatch(fetchBuildings())
    }, [dispatch])

    // Update table data when buildings change
    useEffect(() => {
        setTableData((prev) => ({ ...prev, total: buildings.length }))
        setFilterData((prev) => ({ ...prev, total: buildings.length }))
    }, [buildings])

    // Filter data based on search and filters
    const filteredData = useMemo(() => {
        let filtered = buildingsWithLanguage

        if (searchTerm) {
            filtered = filtered.filter(
                (building) =>
                    building.name
                        .toLowerCase()
                        .includes(searchTerm.toLowerCase()) ||
                    building.displayVillage
                        .toLowerCase()
                        .includes(searchTerm.toLowerCase()) ||
                    building.displayCity
                        .toLowerCase()
                        .includes(searchTerm.toLowerCase()) ||
                    building.displayGovernorate
                        .toLowerCase()
                        .includes(searchTerm.toLowerCase()),
            )
        }

        if (statusFilter) {
            filtered = filtered.filter(
                (building) => building.status.toString() === statusFilter,
            )
        }

        if (governorateFilter) {
            filtered = filtered.filter(
                (building) => building.displayGovernorate === governorateFilter,
            )
        }

        if (cityFilter) {
            filtered = filtered.filter(
                (building) => building.displayCity === cityFilter,
            )
        }

        return filtered
    }, [
        buildingsWithLanguage,
        searchTerm,
        statusFilter,
        governorateFilter,
        cityFilter,
    ])

    // Get unique values for filters (using language-aware data)
    const governorates = useMemo(() => {
        return [
            ...new Set(
                buildingsWithLanguage.map(
                    (building) => building.displayGovernorate,
                ),
            ),
        ].filter(Boolean)
    }, [buildingsWithLanguage])

    const cities = useMemo(() => {
        return [
            ...new Set(
                buildingsWithLanguage.map((building) => building.displayCity),
            ),
        ].filter(Boolean)
    }, [buildingsWithLanguage])

    const villages = useMemo(() => {
        return [
            ...new Set(
                buildingsWithLanguage.map(
                    (building) => building.displayVillage,
                ),
            ),
        ].filter(Boolean)
    }, [buildingsWithLanguage])

    const statuses = useMemo(() => {
        return [
            { value: '1', label: currentLang === 'ar' ? 'معتمد' : 'Approved' },
            {
                value: '0',
                label: currentLang === 'ar' ? 'غير معتمد' : 'Frozen',
            },
        ]
    }, [currentLang])

    // Mutate function for optimistic updates
    const mutate = (newData?: BuildingListState, shouldRevalidate = true) => {
        if (newData) {
            setTableData((prev) => ({ ...prev, total: newData.total }))
            setFilterData((prev) => ({ ...prev, total: newData.total }))
        }
        if (shouldRevalidate) {
            dispatch(fetchBuildings())
        }
    }

    // Selection helpers
    const setSelectAllBuilding = (buildings: BuildingWithDisplay[]) => {
        setSelectedBuilding(buildings)
    }

    const isAllSelected =
        selectedBuilding.length === filteredData.length &&
        filteredData.length > 0
    const isIndeterminate =
        selectedBuilding.length > 0 &&
        selectedBuilding.length < filteredData.length

    const handleSelectAll = (checked: boolean) => {
        if (checked) {
            setSelectedBuilding([...filteredData])
        } else {
            setSelectedBuilding([])
        }
    }

    const handleSelect = (building: BuildingWithDisplay, checked: boolean) => {
        if (checked) {
            setSelectedBuilding((prev) => [...prev, building])
        } else {
            setSelectedBuilding((prev) =>
                prev.filter((b) => b.id !== building.id),
            )
        }
    }

    // Search and filter handlers
    const handleSearch = (term: string) => {
        setSearchTerm(term)
    }

    const handleStatusFilter = (status: string) => {
        setStatusFilter(status)
    }

    const handleGovernorateFilter = (governorate: string) => {
        setGovernorateFilter(governorate)
    }

    const handleCityFilter = (city: string) => {
        setCityFilter(city)
    }

    const clearFilters = () => {
        setSearchTerm('')
        setStatusFilter('')
        setGovernorateFilter('')
        setCityFilter('')
    }

    return {
        // Data
        buildingList: filteredData,
        buildingListTotal: filteredData.length,
        tableData,
        filterData,
        isLoading: loading,
        error,

        // Selection
        selectedBuilding,
        isAllSelected,
        isIndeterminate,

        // Actions
        mutate,
        setTableData,
        setSelectAllBuilding,
        setSelectedBuilding,
        handleSelectAll,
        handleSelect,

        // Search and filters
        searchTerm,
        statusFilter,
        governorateFilter,
        cityFilter,
        handleSearch,
        handleStatusFilter,
        handleGovernorateFilter,
        handleCityFilter,
        clearFilters,

        // Filter options
        governorates,
        cities,
        villages,
        statuses,

        // Language info
        currentLang,
    }
}

export default useBuildingList
