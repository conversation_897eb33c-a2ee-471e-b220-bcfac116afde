import { useState } from 'react'
import { useDispatch } from 'react-redux'
import Dialog from '@/components/ui/Dialog'
import Button from '@/components/ui/Button'
import Notification from '@/components/ui/Notification'
import toast from '@/components/ui/toast'
import BuildingForm from './BuildingForm'
import {
    createBuildingAction,
    updateBuildingAction,
} from '../../../../store/slices/buildingSlice'
import type { AppDispatch } from '../../../../store/store'
import useTranslation from '@/utils/hooks/useTranslation'
import type { Building } from '../../../../services/BuildingService'
import type { BuildingWithDisplay } from '../hooks/useBuildingList'

type BuildingModalProps = {
    isOpen: boolean
    onClose: () => void
    editData?: BuildingWithDisplay
    onSuccess?: () => void
}

const BuildingModal = ({
    isOpen,
    onClose,
    editData,
    onSuccess,
}: BuildingModalProps) => {
    const { t } = useTranslation()
    const dispatch = useDispatch<AppDispatch>()
    const [isSubmitting, setIsSubmitting] = useState(false)

    const isEdit = !!editData

    const handleFormSubmit = async (values: Building) => {
        console.log('Submitted values', values)
        setIsSubmitting(true)

        try {
            if (isEdit && editData?.id) {
                await dispatch(
                    updateBuildingAction({ ...values, id: editData.id }),
                ).unwrap()
                toast.push(
                    <Notification type="success">
                        {t('nav.buildings.buildingUpdated')}
                    </Notification>,
                    { placement: 'top-center' },
                )
            } else {
                await dispatch(createBuildingAction(values)).unwrap()
                toast.push(
                    <Notification type="success">
                        {t('nav.buildings.buildingCreated')}
                    </Notification>,
                    { placement: 'top-center' },
                )
            }

            onSuccess?.()
            onClose()
        } catch {
            toast.push(
                <Notification type="danger">
                    {t('nav.shared.errorOccurred')}
                </Notification>,
                { placement: 'top-center' },
            )
        } finally {
            setIsSubmitting(false)
        }
    }

    const getDefaultValues = (): Partial<Building> => {
        if (isEdit && editData) {
            return {
                id: editData.id,
                name: editData.name || '',
                villageNameAr: editData.villageNameAr || '',
                cityOrDistrictNameAr: editData.cityOrDistrictNameAr || '',
                governorateNameAr: editData.governorateNameAr || '',
                countryNameAr: editData.countryNameAr || '',
                status: editData.status || 0,
            }
        }

        return {
            name: '',
            villageNameAr: '',
            cityOrDistrictNameAr: '',
            governorateNameAr: '',
            countryNameAr: '',
            status: 0,
        }
    }

    return (
        <>
            <Dialog
                isOpen={isOpen}
                shouldCloseOnOverlayClick={true}
                shouldCloseOnEsc={true}
                width={1000}
                className={'h-fit'}
                onClose={onClose}
                onRequestClose={onClose}
            >
                <div className="flex flex-col h-full">
                    <div className="flex items-center justify-center">
                        <h3 className="text-lg font-semibold">
                            {isEdit
                                ? t('nav.shared.edit') +
                                  ' ' +
                                  t('nav.buildings.buildings')
                                : t('nav.shared.add') +
                                  ' ' +
                                  t('nav.buildings.buildings')}
                        </h3>
                    </div>

                    <div className="flex-1 overflow-auto">
                        <BuildingForm
                            newBuilding={!isEdit}
                            defaultValues={getDefaultValues()}
                            onFormSubmit={handleFormSubmit}
                        >
                            <div className="flex items-center justify-between px-4 pt-4  bg-white dark:bg-gray-800">
                                <div>
                                    {isEdit && (
                                        <div className="text-sm text-gray-500 dark:text-gray-400">
                                            {t('nav.shared.id')} :{' '}
                                            {editData?.id}
                                        </div>
                                    )}
                                </div>
                                <Button
                                    variant="solid"
                                    type="submit"
                                    loading={isSubmitting}
                                >
                                    {isEdit
                                        ? t('nav.shared.update')
                                        : t('nav.shared.create')}
                                </Button>
                            </div>
                        </BuildingForm>
                    </div>
                </div>
            </Dialog>
        </>
    )
}

export default BuildingModal
