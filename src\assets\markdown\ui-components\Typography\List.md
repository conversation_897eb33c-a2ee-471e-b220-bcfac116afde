```jsx
const List = () => {
    return (
        <div>
            <div className="mb-6">
                <span className="text-sm text-slate-500 font-mono mb-3 dark:text-slate-400">
                    font-light
                </span>
                <ul className="list-disc list-inside">
                    <li>
                        Now this is a story all about how, my life got flipped
                        turned upside down
                    </li>
                    <li>And I like to take a minute and sit right here</li>
                    <li>
                        I'll tell you how I became the prince of a town called
                        Bel-Air{' '}
                    </li>
                </ul>
            </div>
            <div className="mb-6">
                <span className="text-sm text-slate-500 font-mono mb-3 dark:text-slate-400">
                    font-light
                </span>
                <ul className="list-decimal list-inside">
                    <li>
                        Now this is a story all about how, my life got flipped
                        turned upside down
                    </li>
                    <li>And I like to take a minute and sit right here</li>
                    <li>
                        I'll tell you how I became the prince of a town called
                        Bel-Air{' '}
                    </li>
                </ul>
            </div>
            <div>
                <span className="text-sm text-slate-500 font-mono mb-3 dark:text-slate-400">
                    font-light
                </span>
                <ul className="list-none list-inside">
                    <li>
                        Now this is a story all about how, my life got flipped
                        turned upside down
                    </li>
                    <li>And I like to take a minute and sit right here</li>
                    <li>
                        I'll tell you how I became the prince of a town called
                        Bel-Air{' '}
                    </li>
                </ul>
            </div>
        </div>
    )
}

export default List
```
