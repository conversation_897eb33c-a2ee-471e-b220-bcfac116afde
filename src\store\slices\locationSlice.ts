import { createAsyncThunk, createSlice } from '@reduxjs/toolkit'
import { getCities, getCountries, getDistricts, getGovernorates } from '@/services/LocationService'
import type { Country, Governorate, City, District } from '@/@types/location'

export const fetchCountries = createAsyncThunk('location/fetchCountries', async () => {
   return await getCountries()
})

export const fetchGovernorates = createAsyncThunk('location/fetchGovernorates', async (countryCode: string) => {
    return await getGovernorates(countryCode)
})

export const fetchCities = createAsyncThunk('location/fetchCities', async (governorateCode: string) => {
    return await getCities(governorateCode)
})

export const fetchDistricts = createAsyncThunk('location/fetchDistricts', async (cityCode: string) => {
    return await getDistricts(cityCode)
})




const initialState = {
    countries: [] as Country[],
    governorates: [] as Governorate[],
    cities: [] as City[],
    districts: [] as District[],
    isLoadingCountries: false,
    isLoadingGovernorates: false,
    isLoadingCities: false,
    isLoadingDistricts: false,
    countriesError: false,
    governoratesError: false,
    citiesError: false,
    districtsError: false,
}


export const locationSlice = createSlice({
    name: 'location', 
    initialState,
    reducers: {
        clearLocation: (state) => {
            state.countries = []
            state.governorates = []
            state.cities = []
            state.districts = []
            state.isLoadingCountries = false
            state.isLoadingGovernorates = false
            state.isLoadingCities = false
            state.isLoadingDistricts = false
            state.countriesError = false
            state.governoratesError = false
            state.citiesError = false
            state.districtsError = false
        }
    },
    extraReducers: (builder) => {
        builder
        .addCase(fetchCountries.pending , (state) => {
            state.isLoadingCountries = true
            state.countriesError = false
        })
        .addCase(fetchCountries.fulfilled, (state, action) => {  
            state.countries = action.payload
            state.isLoadingCountries = false
            state.countriesError = false
        })
        .addCase(fetchCountries.rejected, (state) => {
            state.isLoadingCountries = false
            state.countriesError = true
        })
        .addCase(fetchGovernorates.pending, (state) => {
            state.isLoadingGovernorates = true
            state.governoratesError = false
        })
        .addCase(fetchGovernorates.fulfilled, (state, action) => {
            state.governorates = action.payload
            state.isLoadingGovernorates = false
            state.governoratesError = false
        })
        .addCase(fetchGovernorates.rejected, (state) => {
            state.isLoadingGovernorates = false
            state.governoratesError = true
        })
        .addCase(fetchCities.pending, (state) => {
            state.isLoadingCities = true
            state.citiesError = false
        })
        .addCase(fetchCities.fulfilled, (state, action) => {
            state.cities = action.payload
            state.isLoadingCities = false
            state.citiesError = false
        })
        .addCase(fetchCities.rejected, (state) => {
            state.isLoadingCities = false
            state.citiesError = true
        })
        .addCase(fetchDistricts.pending, (state) => {
            state.isLoadingDistricts = true
            state.districtsError = false
        })
        .addCase(fetchDistricts.fulfilled, (state, action) => {
            state.districts = action.payload
            state.isLoadingDistricts = false
                state.districtsError = false
        })
        .addCase(fetchDistricts.rejected, (state) => {
            state.isLoadingDistricts = false
            state.districtsError = true
        })
    },
})

export const locationReducer = locationSlice.reducer


