
import ApiService from './ApiService'
import type { Country, Governorate, City, District } from '@/@types/location'

export async function getCountries(): Promise<Country[]> {      
    const response = await ApiService.get<{ countries: Country[] }>('/Location/countries')
    return response.countries || []
}

export async function getGovernorates(countryCode: string): Promise<Governorate[]> {
    
        const response = await ApiService.get<{ governorates: Governorate[] }>(
            `/Location/countries/${countryCode}/governorates`
        )
        return response.governorates || []
}

export async function getCities(governorateCode: string): Promise<City[]> {
        const response = await ApiService.get<{ cities: City[] }>(
            `/Location/governorates/${governorateCode}/cities`
        )
        return response.cities || []

}

export async function getDistricts(cityCode: string): Promise<District[]> {
        const response = await ApiService.get<{ districts: District[] }>(
            `/Location/cities/${cityCode}/districts`
        )
        return response.districts || []

}


