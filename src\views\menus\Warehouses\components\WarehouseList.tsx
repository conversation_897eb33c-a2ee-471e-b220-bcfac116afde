import React, { useEffect, useState } from 'react';
import Card from '@/components/ui/Card';
import Input from '@/components/ui/Input';
import Button from '@/components/ui/Button';
import Badge from '@/components/ui/Badge';
import { TbSearch, TbPlus, TbFilter, TbBox, TbCalendar, TbLayoutGrid, TbTable } from 'react-icons/tb';
import { useWarehouseStore } from '../store/warehouseStore';
import { capacity, formatNumber, formatDate, formatPercentage } from '../utils';
import type { Warehouse } from '../types';

interface WarehouseListProps {
  onOpenCreate: () => void;
  onOpenDetail: (warehouse: Warehouse) => void;
}

type ViewMode = 'cards' | 'table';

const WarehouseCard: React.FC<{ warehouse: Warehouse; onOpenDetail: (warehouse: Warehouse) => void }> = ({ warehouse, onOpenDetail }) => {
  const cap = capacity(warehouse);

  return (
    <Card 
      className="hover:shadow-lg transition-all duration-200 cursor-pointer group"
      onClick={() => onOpenDetail(warehouse)}
    >
      <div className="p-6">
        <div className="flex items-start justify-between mb-4">
          <div className="flex items-center gap-3">
            <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center text-white">
              <TbFilter size={24} />
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                {warehouse.name}
              </h3>
              {warehouse.description && (
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                  {warehouse.description}
                </p>
              )}
              {warehouse.city && warehouse.governorate && (
                <p className="text-xs text-gray-500 dark:text-gray-500 mt-1">
                  📍 {warehouse.city}, {warehouse.governorate}
                </p>
              )}
            </div>
          </div>
          <Badge className="bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300">
            Active
          </Badge>
        </div>

        <div className="grid grid-cols-2 gap-4 mb-4">
          <div className="text-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
              {formatNumber(cap.totalBoxes)}
            </div>
            <div className="text-xs text-gray-600 dark:text-gray-400">Total Boxes</div>
          </div>
          <div className="text-center p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <div className="text-2xl font-bold text-green-600 dark:text-green-400">
              {formatNumber(cap.filledBoxes)}
            </div>
            <div className="text-xs text-gray-600 dark:text-gray-400">Filled Boxes</div>
          </div>
        </div>

        <div className="flex items-center justify-between text-sm text-gray-600 dark:text-gray-400">
          <div className="flex items-center gap-1">
            <TbCalendar size={16} />
            <span>Created {formatDate(warehouse.createdAt)}</span>
          </div>
          <div className="flex items-center gap-1">
            <TbBox size={16} />
            <span>{warehouse.areasCount}A × {warehouse.unitsPerArea}U × {warehouse.shelvesPerUnit}S × {warehouse.boxesPerShelf}B</span>
          </div>
        </div>
      </div>
    </Card>
  );
};

const WarehouseTableView: React.FC<{ warehouses: Warehouse[]; onOpenDetail: (warehouse: Warehouse) => void }> = ({ warehouses, onOpenDetail }) => {
  return (
    <Card className="overflow-hidden">
      <div className="overflow-x-auto">
        <table className="w-full">
          <thead className="bg-gray-50 dark:bg-gray-800">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Warehouse
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Location
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Structure
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Capacity
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Utilization
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Created
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Status
              </th>
            </tr>
          </thead>
          <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
            {warehouses.map((warehouse) => {
              const cap = capacity(warehouse);
              return (
                <tr 
                  key={warehouse.id} 
                  className="hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer transition-colors"
                  onClick={() => onOpenDetail(warehouse)}
                >
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center text-white mr-3">
                        <TbFilter size={20} />
                      </div>
                      <div>
                        <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                          {warehouse.name}
                        </div>
                        {warehouse.description && (
                          <div className="text-sm text-gray-500 dark:text-gray-400">
                            {warehouse.description}
                          </div>
                        )}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900 dark:text-gray-100">
                      {warehouse.city && warehouse.governorate ? (
                        <>
                          <div className="font-medium">{warehouse.city}</div>
                          <div className="text-gray-500 dark:text-gray-400">{warehouse.governorate}</div>
                        </>
                      ) : (
                        <span className="text-gray-400">Not specified</span>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                    {warehouse.areasCount}A × {warehouse.unitsPerArea}U × {warehouse.shelvesPerUnit}S × {warehouse.boxesPerShelf}B
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900 dark:text-gray-100">
                      <div className="font-medium">{formatNumber(cap.totalBoxes)} total</div>
                      <div className="text-gray-500 dark:text-gray-400">{formatNumber(cap.filledBoxes)} filled</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900 dark:text-gray-100">
                      <div className="font-medium">{formatPercentage(cap.utilizationRate)}</div>
                      <div className="text-gray-500 dark:text-gray-400">{formatNumber(cap.emptyBoxes)} empty</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                    {formatDate(warehouse.createdAt)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <Badge className="bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300">
                      Active
                    </Badge>
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>
    </Card>
  );
};

const WarehouseList: React.FC<WarehouseListProps> = ({ onOpenCreate, onOpenDetail }) => {
  const { 
    filters, 
    isLoading, 
    error,
    fetchWarehouses, 
    getFilteredWarehouses, 
    setFilters 
  } = useWarehouseStore();

  const [showFilters, setShowFilters] = useState(false);
  const [viewMode, setViewMode] = useState<ViewMode>('cards');

  useEffect(() => {
    fetchWarehouses();
  }, [fetchWarehouses]);

  const filteredWarehouses = getFilteredWarehouses();

  if (error) {
    return (
      <div className="text-center py-12">
        <div className="text-red-500 mb-4">{error}</div>
        <Button onClick={fetchWarehouses}>Retry</Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
            Warehouse Management
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Manage your archiving system warehouses and storage locations
          </p>
        </div>
        <Button 
          variant="solid" 
          onClick={onOpenCreate}
          className="flex items-center gap-2"
        >
          <TbPlus size={20} />
          Create Warehouse
        </Button>
      </div>

      {/* Search and Filters */}
      <Card className="p-4">
        <div className="flex flex-col lg:flex-row gap-4">
          <div className="flex-1">
            <div className="relative">
              <TbSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={20} />
              <Input
                placeholder="Search warehouses..."
                value={filters.search}
                onChange={(e) => setFilters({ search: e.target.value })}
                className="pl-10"
              />
            </div>
          </div>
          <div className="flex gap-2">
            <Button
              variant="plain"
              onClick={() => setShowFilters(!showFilters)}
              className="flex items-center gap-2"
            >
              <TbFilter size={16} />
              Filters
            </Button>
            <Button
              variant="plain"
              onClick={() => setFilters({ search: '', minCapacity: 0, maxCapacity: Infinity })}
            >
              Clear
            </Button>
          </div>
        </div>

        {showFilters && (
          <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Min Capacity
                </label>
                <Input
                  type="number"
                  placeholder="0"
                  value={filters.minCapacity || ''}
                  onChange={(e) => setFilters({ minCapacity: parseInt(e.target.value) || 0 })}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Max Capacity
                </label>
                <Input
                  type="number"
                  placeholder="∞"
                  value={filters.maxCapacity === Infinity ? '' : filters.maxCapacity}
                  onChange={(e) => setFilters({ maxCapacity: parseInt(e.target.value) || Infinity })}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Created After
                </label>
                <Input
                  type="date"
                  value={filters.createdAfter}
                  onChange={(e) => setFilters({ createdAfter: e.target.value })}
                />
              </div>
            </div>
          </div>
        )}
      </Card>

      {/* Results and View Toggle */}
      <div className="flex items-center justify-between mb-4">
        <div className="text-sm text-gray-600 dark:text-gray-400">
          {filteredWarehouses.length} warehouse{filteredWarehouses.length !== 1 ? 's' : ''} found
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant={viewMode === 'cards' ? 'solid' : 'plain'}
            size="sm"
            onClick={() => setViewMode('cards')}
            className="flex items-center gap-2"
          >
            <TbLayoutGrid size={16} />
            Cards
          </Button>
          <Button
            variant={viewMode === 'table' ? 'solid' : 'plain'}
            size="sm"
            onClick={() => setViewMode('table')}
            className="flex items-center gap-2"
          >
            <TbTable size={16} />
            Table
          </Button>
        </div>
      </div>

      {/* Warehouse Display */}
      {isLoading ? (
        viewMode === 'cards' ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[1, 2, 3, 4, 5, 6].map((i) => (
              <Card key={i} className="p-6 animate-pulse">
                <div className="flex items-center gap-3 mb-4">
                  <div className="w-12 h-12 bg-gray-200 dark:bg-gray-700 rounded-xl"></div>
                  <div className="flex-1">
                    <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded mb-2"></div>
                    <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-2/3"></div>
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4 mb-4">
                  <div className="h-16 bg-gray-200 dark:bg-gray-700 rounded"></div>
                  <div className="h-16 bg-gray-200 dark:bg-gray-700 rounded"></div>
                </div>
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
              </Card>
            ))}
          </div>
        ) : (
          <Card className="p-6 animate-pulse">
            <div className="space-y-4">
              {[1, 2, 3, 4, 5].map((i) => (
                <div key={i} className="flex items-center gap-4">
                  <div className="w-10 h-10 bg-gray-200 dark:bg-gray-700 rounded-lg"></div>
                  <div className="flex-1 space-y-2">
                    <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/4"></div>
                    <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/3"></div>
                  </div>
                  <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-20"></div>
                  <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-24"></div>
                  <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded w-16"></div>
                </div>
              ))}
            </div>
          </Card>
        )
      ) : filteredWarehouses.length === 0 ? (
        <Card className="text-center py-12">
          <TbFilter size={48} className="mx-auto text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
            No warehouses found
          </h3>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            {filters.search || filters.minCapacity > 0 || filters.maxCapacity < Infinity
              ? 'Try adjusting your search or filters'
              : 'Get started by creating your first warehouse'
            }
          </p>
          {!filters.search && filters.minCapacity === 0 && filters.maxCapacity === Infinity && (
            <Button onClick={onOpenCreate}>
              Create First Warehouse
            </Button>
          )}
        </Card>
      ) : viewMode === 'cards' ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredWarehouses.map((warehouse) => (
            <WarehouseCard key={warehouse.id} warehouse={warehouse} onOpenDetail={onOpenDetail} />
          ))}
        </div>
      ) : (
        <WarehouseTableView warehouses={filteredWarehouses} onOpenDetail={onOpenDetail} />
      )}
    </div>
  );
};

export default WarehouseList;
