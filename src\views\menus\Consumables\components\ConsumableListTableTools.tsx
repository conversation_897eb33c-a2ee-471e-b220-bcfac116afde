import ConsumableListSearch from './ConsumableListSearch'
import ConsumableTableFilter from './ConsumableTableFilter'
import useConsumableList from '../hooks/useConsumableList'
import cloneDeep from 'lodash/cloneDeep'

const ConsumableListTableTools = () => {
    const { tableData, setTableData } = useConsumableList()

    const handleInputChange = (val: string) => {
        const newTableData = cloneDeep(tableData)
        newTableData.query = val
        newTableData.pageIndex = 1
        if (typeof val === 'string' && val.length > 1) {
            setTableData(newTableData)
        }

        if (typeof val === 'string' && val.length === 0) {
            setTableData(newTableData)
        }
    }

    return (
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-2">
            <ConsumableListSearch onInputChange={handleInputChange} />
            <ConsumableTableFilter />
        </div>
    )
}

export default ConsumableListTableTools
