import Card from '@/components/ui/Card'
import Input from '@/components/ui/Input'
import { FormItem } from '@/components/ui/Form'
import { Controller } from 'react-hook-form'
import type { FormSectionBaseProps } from '../types'
import { useTranslation } from 'react-i18next'
import SimpleLocationSelector from '@/components/shared/SimpleLocationSelector'

type BasicInfoSectionProps = FormSectionBaseProps

const BasicInfoSection = ({ control, errors }: BasicInfoSectionProps) => {
    const { t } = useTranslation()

    return (
        <Card className="w-full p-0">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormItem
                    className="mb-4"
                    label={t('nav.buildings.name')}
                    invalid={Boolean(errors.name)}
                    errorMessage={errors.name?.message}
                >
                    <Controller
                        name="name"
                        control={control}
                        render={({ field }) => (
                            <Input
                                type="text"
                                autoComplete="off"
                                placeholder={t('nav.buildings.enterName')}
                                {...field}
                            />
                        )}
                    />
                </FormItem>

                <FormItem
                    className="mb-4"
                    label={t('nav.shared.description')}
                    invalid={Boolean(errors.description)}
                    errorMessage={errors.description?.message}
                >
                    <Controller
                        name="description"
                        control={control}
                        render={({ field }) => (
                            <Input
                                type="text"
                                autoComplete="off"
                                placeholder={t('nav.shared.enterDescription')}
                                {...field}
                            />
                        )}
                    />
                </FormItem>
            </div>

            {/* Location Selector Component */}
            <SimpleLocationSelector
                control={control}
                errors={errors}
                gridCols="grid-cols-1 md:grid-cols-2"
                className="mb-4"
            />

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <FormItem
                    className="mb-4"
                    label={t('nav.Form.email')}
                    invalid={Boolean(errors.email)}
                    errorMessage={errors.email?.message}
                >
                    <Controller
                        name="email"
                        control={control}
                        render={({ field }) => (
                            <Input
                                type="text"
                                autoComplete="off"
                                placeholder={t('nav.Form.enterEmail')}
                                {...field}
                            />
                        )}
                    />
                </FormItem>

                <FormItem
                    className="mb-4"
                    label={t('nav.Form.phone')}
                    invalid={Boolean(errors.phone)}
                    errorMessage={errors.phone?.message}
                >
                    <Controller
                        name="phone"
                        control={control}
                        render={({ field }) => (
                            <Input
                                type="text"
                                autoComplete="off"
                                placeholder={t('nav.Form.enterPhone')}
                                {...field}
                            />
                        )}
                    />
                </FormItem>
            </div>
        </Card>
    )
}

export default BasicInfoSection
