import { RootState } from "../store";
import { createSelector } from "@reduxjs/toolkit";
import { useLocaleStore } from "../localeStore";
import type { StockColumns } from "@/@types/stocks";

// Basic selectors
export const selectStockState = (state: RootState) => state.stock;

export const selectStocks = (state: RootState) => selectStockState(state).stocks;
export const selectStock = (state: RootState) => selectStockState(state).stock;
export const selectLoading = (state: RootState) => selectStockState(state).loading;
export const selectError = (state: RootState) => selectStockState(state).error;

// Language-aware selectors
export const selectStocksWithLanguage = createSelector(
    [selectStocks],
    (stocks) => {
        const { currentLang } = useLocaleStore.getState();
        const isArabic = currentLang === 'ar';
        
        return stocks.map(stock => ({
            ...stock,
            // Language-specific fields
            cityName: isArabic ? stock.cityOrDistrictNameAr : stock.cityOrDistrictNameEn || stock.cityOrDistrictNameAr,
            governorateName: isArabic ? stock.governorateNameAr : stock.governorateNameEn || stock.governorateNameAr,
            countryName: isArabic ? stock.countryNameAr : stock.countryNameEn || stock.countryNameAr,
            // Display fields for UI
            displayCity: isArabic ? stock.cityOrDistrictNameAr : stock.cityOrDistrictNameEn || stock.cityOrDistrictNameAr,
            displayGovernorate: isArabic ? stock.governorateNameAr : stock.governorateNameEn || stock.governorateNameAr,
            displayCountry: isArabic ? stock.countryNameAr : stock.countryNameEn || stock.countryNameAr,
        }));
    }
);

// Language-aware selector for single stock
export const selectStockWithLanguage = createSelector(
    [selectStock],
    (stock) => {
        if (!stock) return null;
        
        const { currentLang } = useLocaleStore.getState();
        const isArabic = currentLang === 'ar';
        
        return {
            ...stock,
            cityName: isArabic ? stock.cityOrDistrictNameAr : stock.cityOrDistrictNameEn || stock.cityOrDistrictNameAr,
            governorateName: isArabic ? stock.governorateNameAr : stock.governorateNameEn || stock.governorateNameAr,
            countryName: isArabic ? stock.countryNameAr : stock.countryNameEn || stock.countryNameAr,
            displayCity: isArabic ? stock.cityOrDistrictNameAr : stock.cityOrDistrictNameEn || stock.cityOrDistrictNameAr,
            displayGovernorate: isArabic ? stock.governorateNameAr : stock.governorateNameEn || stock.governorateNameAr,
            displayCountry: isArabic ? stock.countryNameAr : stock.countryNameEn || stock.countryNameAr,
        };
    }
);

// Derived selectors
export const selectStocksCount = createSelector(
    [selectStocks],
    (stocks) => stocks.length
);

export const selectActiveStocks = createSelector(
    [selectStocks],
    (stocks) => stocks.filter(stock => stock.status === 1)
);

export const selectInactiveStocks = createSelector(
    [selectStocks],
    (stocks) => stocks.filter(stock => stock.status === 0)
);

export const selectStocksByGovernorate = createSelector(
    [selectStocks, (state: RootState, governorate: string) => governorate],
    (stocks, governorate) => stocks.filter(stock => stock.governorateNameAr === governorate)
);

export const selectStocksByCity = createSelector(
    [selectStocks, (state: RootState, city: string) => city],
    (stocks, city) => stocks.filter(stock => stock.cityOrDistrictNameAr === city)
);

export const selectStocksByStatus = createSelector(
    [selectStocks, (state: RootState, status: number) => status],
    (stocks, status) => stocks.filter(stock => stock.status === status)
);

export const selectStocksByManager = createSelector(
    [selectStocks, (state: RootState, managerName: string) => managerName],
    (stocks, managerName) => stocks.filter(stock => 
        stock.managerName.toLowerCase().includes(managerName.toLowerCase())
    )
);

export const selectUniqueGovernorates = createSelector(
    [selectStocks],
    (stocks) => [...new Set(stocks.map(stock => stock.governorateNameAr))].filter(Boolean)
);

export const selectUniqueCities = createSelector(
    [selectStocks],
    (stocks) => [...new Set(stocks.map(stock => stock.cityOrDistrictNameAr))].filter(Boolean)
);

export const selectUniqueVillages = createSelector(
    [selectStocks],
    (stocks) => [...new Set(stocks.map(stock => stock.villageNameAr))].filter(Boolean)
);

export const selectUniqueManagers = createSelector(
    [selectStocks],
    (stocks) => [...new Set(stocks.map(stock => stock.managerName))].filter(Boolean)
);

// Search selector
export const selectStocksSearch = createSelector(
    [selectStocks, (state: RootState, searchTerm: string) => searchTerm],
    (stocks, searchTerm) => {
        if (!searchTerm) return stocks;
        
        const term = searchTerm.toLowerCase();
        return stocks.filter(stock =>
            stock.name.toLowerCase().includes(term) ||
            stock.villageNameAr.toLowerCase().includes(term) ||
            stock.cityOrDistrictNameAr.toLowerCase().includes(term) ||
            stock.governorateNameAr.toLowerCase().includes(term) ||
            stock.managerName.toLowerCase().includes(term)
        );
    }
);

// Filter selector
export const selectStocksWithFilters = createSelector(
    [
        selectStocks,
        (state: RootState, filters: { 
            status?: string; 
            governorate?: string; 
            city?: string;
            manager?: string;
        }) => filters
    ],
    (stocks, filters) => {
        let filtered = stocks;

        if (filters.status) {
            filtered = filtered.filter(stock => stock.status.toString() === filters.status);
        }

        if (filters.governorate) {
            filtered = filtered.filter(stock => stock.governorateNameAr === filters.governorate);
        }

        if (filters.city) {
            filtered = filtered.filter(stock => stock.cityOrDistrictNameAr === filters.city);
        }

        if (filters.manager) {
            filtered = filtered.filter(stock => 
                stock.managerName.toLowerCase().includes(filters.manager!.toLowerCase())
            );
        }

        return filtered;
    }
);

// Sort selector
export const selectStocksSorted = createSelector(
    [
        selectStocks,
        (state: RootState, sortConfig: { 
            field: keyof StockColumns; 
            direction: 'asc' | 'desc' 
        }) => sortConfig
    ],
    (stocks, sortConfig) => {
        const { field, direction } = sortConfig;
        
        return [...stocks].sort((a, b) => {
            let aValue = a[field];
            let bValue = b[field];
            
            // Handle string comparison
            if (typeof aValue === 'string' && typeof bValue === 'string') {
                aValue = aValue.toLowerCase();
                bValue = bValue.toLowerCase();
            }
            
            if (aValue < bValue) return direction === 'asc' ? -1 : 1;
            if (aValue > bValue) return direction === 'asc' ? 1 : -1;
            return 0;
        });
    }
);

// Combined search, filter, and sort selector
export const selectStocksProcessed = createSelector(
    [
        selectStocks,
        (state: RootState, config: {
            searchTerm?: string;
            filters?: { 
                status?: string; 
                governorate?: string; 
                city?: string;
                manager?: string;
            };
            sortConfig?: { 
                field: keyof StockColumns; 
                direction: 'asc' | 'desc' 
            };
        }) => config
    ],
    (stocks, config) => {
        let processed = stocks;
        
        // Apply search
        if (config.searchTerm) {
            const term = config.searchTerm.toLowerCase();
            processed = processed.filter(stock =>
                stock.name.toLowerCase().includes(term) ||
                stock.villageNameAr.toLowerCase().includes(term) ||
                stock.cityOrDistrictNameAr.toLowerCase().includes(term) ||
                stock.governorateNameAr.toLowerCase().includes(term) ||
                stock.managerName.toLowerCase().includes(term)
            );
        }
        
        // Apply filters
        if (config.filters) {
            const { status, governorate, city, manager } = config.filters;
            
            if (status) {
                processed = processed.filter(stock => stock.status.toString() === status);
            }
            
            if (governorate) {
                processed = processed.filter(stock => stock.governorateNameAr === governorate);
            }
            
            if (city) {
                processed = processed.filter(stock => stock.cityOrDistrictNameAr === city);
            }
            
            if (manager) {
                processed = processed.filter(stock => 
                    stock.managerName.toLowerCase().includes(manager.toLowerCase())
                );
            }
        }
        
        // Apply sorting
        if (config.sortConfig) {
            const { field, direction } = config.sortConfig;
            
            processed = [...processed].sort((a, b) => {
                let aValue = a[field];
                let bValue = b[field];
                
                if (typeof aValue === 'string' && typeof bValue === 'string') {
                    aValue = aValue.toLowerCase();
                    bValue = bValue.toLowerCase();
                }
                
                if (aValue < bValue) return direction === 'asc' ? -1 : 1;
                if (aValue > bValue) return direction === 'asc' ? 1 : -1;
                return 0;
            });
        }
        
        return processed;
    }
);

// Column visibility selector based on language
export const selectStockColumns = createSelector(
    [selectStocks],
    () => {
        const { currentLang } = useLocaleStore.getState();
        const isArabic = currentLang === 'ar';
        
        return [
            { key: 'id', label: 'ID', visible: true, sortable: true },
            { key: 'name', label: isArabic ? 'اسم المستودع' : 'Stock Name', visible: true, sortable: true },
            { key: 'villageNameAr', label: isArabic ? 'اسم القرية' : 'Village', visible: true, sortable: true },
            { key: 'cityOrDistrictNameAr', label: isArabic ? 'اسم المدينة/المنطقة' : 'City/District', visible: true, sortable: true },
            { key: 'governorateNameAr', label: isArabic ? 'اسم المحافظة' : 'Governorate', visible: true, sortable: true },
            { key: 'countryNameAr', label: isArabic ? 'اسم البلد' : 'Country', visible: true, sortable: true },
            { key: 'managerName', label: isArabic ? 'اسم المدير' : 'Manager', visible: true, sortable: true },
            { key: 'status', label: isArabic ? 'الحالة' : 'Status', visible: true, sortable: true },
        ];
    }
);

// Stats selector
export const selectStocksStats = createSelector(
    [selectStocks],
    (stocks) => {
        const total = stocks.length;
        const active = stocks.filter(stock => stock.status === 1).length;
        const inactive = stocks.filter(stock => stock.status === 0).length;
        
        return {
            total,
            active,
            inactive,
            activePercentage: total > 0 ? Math.round((active / total) * 100) : 0,
            inactivePercentage: total > 0 ? Math.round((inactive / total) * 100) : 0
        };
    }
);

// Stock by ID selector
export const selectStockById = createSelector(
    [selectStocks, (state: RootState, id: number) => id],
    (stocks, id) => stocks.find(stock => stock.id === id)
);

// Pagination selector
export const selectStocksPaginated = createSelector(
    [
        selectStocks,
        (state: RootState, pagination: { page: number; pageSize: number }) => pagination
    ],
    (stocks, pagination) => {
        const { page, pageSize } = pagination;
        const startIndex = (page - 1) * pageSize;
        const endIndex = startIndex + pageSize;
        
        return {
            data: stocks.slice(startIndex, endIndex),
            total: stocks.length,
            page,
            pageSize,
            totalPages: Math.ceil(stocks.length / pageSize)
        };
    }
);


