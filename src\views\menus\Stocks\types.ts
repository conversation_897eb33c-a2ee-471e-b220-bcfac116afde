import type { StockColumns } from '@/@types/stocks'

// Enhanced Stock interface with display fields for UI
export interface StockWithDisplay extends StockColumns {
    cityName: string
    governorateName: string
    countryName: string
    displayCity: string
    displayGovernorate: string
    displayCountry: string
}

// Stock list state interface
export interface StockListState {
    list: StockColumns[]
    total: number
}

// Stock filter options
export interface StockFilterOptions {
    status: string[]
    governorate: string[]
    city: string[]
    manager: string[]
}
