import type { Warehouse, Layout, LocationIndices, Capacity, Box, BoxStatus } from './types';
import { WAREHOUSE_LIMITS } from './types';

/**
 * Generate a complete layout hierarchy from warehouse counts with box status
 */
export function generateLayout(warehouse: Warehouse): Layout {
  const { areasCount, unitsPerArea, shelvesPerUnit, boxesPerShelf, existingBoxes = 0 } = warehouse;
  
  let remainingBoxes = existingBoxes;
  
  const areas = Array.from({ length: areasCount }, (_, areaIdx) => ({
    id: `A${areaIdx + 1}`,
    units: Array.from({ length: unitsPerArea }, (_, unitIdx) => ({
      id: `A${areaIdx + 1}-U${unitIdx + 1}`,
      shelves: Array.from({ length: shelvesPerUnit }, (_, shelfIdx) => ({
        id: `A${areaIdx + 1}-U${unitIdx + 1}-S${shelfIdx + 1}`,
        boxes: Array.from({ length: boxesPerShelf }, (_, boxIdx) => {
          const boxId = `A${areaIdx + 1}-U${unitIdx + 1}-S${shelfIdx + 1}-B${boxIdx + 1}`;
          const status: BoxStatus = remainingBoxes > 0 ? 'filled' : 'empty';
          
          if (remainingBoxes > 0) {
            remainingBoxes--;
          }
          
          const box: Box = {
            id: boxId,
            status,
            content: status === 'filled' ? `Document Box ${boxId}` : undefined,
          };
          
          return box;
        }),
      })),
    })),
  }));

  return { areas };
}

/**
 * Parse a location ID path and return indices
 */
export function locate(path: string): LocationIndices | null {
  const match = path.match(/^A(\d+)-U(\d+)-S(\d+)-B(\d+)$/);
  if (!match) return null;

  return {
    areaIdx: parseInt(match[1]) - 1,
    unitIdx: parseInt(match[2]) - 1,
    shelfIdx: parseInt(match[3]) - 1,
    boxIdx: parseInt(match[4]) - 1,
  };
}

/**
 * Calculate total capacity from warehouse counts with utilization
 */
export function capacity(warehouse: Warehouse): Capacity {
  const { areasCount, unitsPerArea, shelvesPerUnit, boxesPerShelf, existingBoxes = 0 } = warehouse;
  
  const totalBoxes = areasCount * unitsPerArea * shelvesPerUnit * boxesPerShelf;
  const filledBoxes = Math.min(existingBoxes, totalBoxes);
  const emptyBoxes = totalBoxes - filledBoxes;
  const utilizationRate = totalBoxes > 0 ? (filledBoxes / totalBoxes) * 100 : 0;
  
  return {
    totalAreas: areasCount,
    totalUnits: areasCount * unitsPerArea,
    totalShelves: areasCount * unitsPerArea * shelvesPerUnit,
    totalBoxes,
    filledBoxes,
    emptyBoxes,
    utilizationRate,
  };
}

/**
 * Format a number with thousands separator
 */
export function formatNumber(num: number): string {
  return num.toLocaleString();
}

/**
 * Format percentage for display
 */
export function formatPercentage(num: number): string {
  return `${num.toFixed(1)}%`;
}

/**
 * Format date for display
 */
export function formatDate(dateString: string): string {
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  });
}

/**
 * Format date and time for display
 */
export function formatDateTime(dateString: string): string {
  return new Date(dateString).toLocaleString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  });
}

/**
 * Validate warehouse counts against limits
 */
export function validateWarehouseCounts(counts: Partial<Warehouse>): string[] {
  const errors: string[] = [];

  if (counts.areasCount !== undefined) {
    if (counts.areasCount < WAREHOUSE_LIMITS.MIN_AREAS) {
      errors.push(`Areas count must be at least ${WAREHOUSE_LIMITS.MIN_AREAS}`);
    }
    if (counts.areasCount > WAREHOUSE_LIMITS.MAX_AREAS) {
      errors.push(`Areas count cannot exceed ${WAREHOUSE_LIMITS.MAX_AREAS}`);
    }
  }

  if (counts.unitsPerArea !== undefined) {
    if (counts.unitsPerArea < WAREHOUSE_LIMITS.MIN_UNITS_PER_AREA) {
      errors.push(`Units per area must be at least ${WAREHOUSE_LIMITS.MIN_UNITS_PER_AREA}`);
    }
    if (counts.unitsPerArea > WAREHOUSE_LIMITS.MAX_UNITS_PER_AREA) {
      errors.push(`Units per area cannot exceed ${WAREHOUSE_LIMITS.MAX_UNITS_PER_AREA}`);
    }
  }

  if (counts.shelvesPerUnit !== undefined) {
    if (counts.shelvesPerUnit < WAREHOUSE_LIMITS.MIN_SHELVES_PER_UNIT) {
      errors.push(`Shelves per unit must be at least ${WAREHOUSE_LIMITS.MIN_SHELVES_PER_UNIT}`);
    }
    if (counts.shelvesPerUnit > WAREHOUSE_LIMITS.MAX_SHELVES_PER_UNIT) {
      errors.push(`Shelves per unit cannot exceed ${WAREHOUSE_LIMITS.MAX_SHELVES_PER_UNIT}`);
    }
  }

  if (counts.boxesPerShelf !== undefined) {
    if (counts.boxesPerShelf < WAREHOUSE_LIMITS.MIN_BOXES_PER_SHELF) {
      errors.push(`Boxes per shelf must be at least ${WAREHOUSE_LIMITS.MIN_BOXES_PER_SHELF}`);
    }
    if (counts.boxesPerShelf > WAREHOUSE_LIMITS.MAX_BOXES_PER_SHELF) {
      errors.push(`Boxes per shelf cannot exceed ${WAREHOUSE_LIMITS.MAX_BOXES_PER_SHELF}`);
    }
  }

  // Validate existing boxes against total capacity
  if (counts.existingBoxes !== undefined) {
    const totalCapacity = (counts.areasCount || 1) * 
                         (counts.unitsPerArea || 1) * 
                         (counts.shelvesPerUnit || 1) * 
                         (counts.boxesPerShelf || 1);
    
    if (counts.existingBoxes > totalCapacity) {
      errors.push(`Existing boxes (${counts.existingBoxes}) cannot exceed total capacity (${totalCapacity})`);
    }
    if (counts.existingBoxes < 0) {
      errors.push('Existing boxes cannot be negative');
    }
  }

  return errors;
}

/**
 * Check if reducing counts would orphan existing boxes
 */
export function wouldOrphanBoxes(
  currentWarehouse: Warehouse,
  newCounts: Partial<Warehouse>
): boolean {
  const currentCap = capacity(currentWarehouse);
  const newCap = capacity({ ...currentWarehouse, ...newCounts });

  return newCap.totalBoxes < currentCap.filledBoxes;
}

/**
 * Generate a flattened list of all boxes for table view
 */
export function flattenLayout(layout: Layout): Array<{
  areaId: string;
  unitId: string;
  shelfId: string;
  boxId: string;
  fullPath: string;
  status: BoxStatus;
  content?: string;
}> {
  const result: Array<{
    areaId: string;
    unitId: string;
    shelfId: string;
    boxId: string;
    fullPath: string;
    status: BoxStatus;
    content?: string;
  }> = [];

  layout.areas.forEach(area => {
    area.units.forEach(unit => {
      unit.shelves.forEach(shelf => {
        shelf.boxes.forEach(box => {
          result.push({
            areaId: area.id,
            unitId: unit.id,
            shelfId: shelf.id,
            boxId: box.id,
            fullPath: box.id,
            status: box.status,
            content: box.content,
          });
        });
      });
    });
  });

  return result;
}
