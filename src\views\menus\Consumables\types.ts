import type { Control, FieldErrors } from 'react-hook-form'

export type CountRange = {
    from: string | number
    to: string | number
}

export type Consumables = {
    id?: string
    category: string
    count: number | string | CountRange
    description: string
    wareHouse: string
    status: string
    attachments: string
    assignedBy?: string
    createdAt?: string
}

export type FormSectionBaseProps = {
    control: Control<Consumables>
    errors: FieldErrors<Consumables>
}

export type Filter = {
    consumableStatus: string
    consumableType: string[]
    visibleColumns: string[]
}

export type GetConsumableListResponse = {
    list: Consumables[]
    total: number
}
