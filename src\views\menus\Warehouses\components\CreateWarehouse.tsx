import React, { useState, useEffect } from 'react';
import Card from '@/components/ui/Card';
import Input from '@/components/ui/Input';
import Button from '@/components/ui/Button';
import { TbArrowLeft,TbCheck, TbX } from 'react-icons/tb';
import { useWarehouseStore } from '../store/warehouseStore';
import { generateLayout, capacity, validateWarehouseCounts, formatNumber } from '../utils';
import { WAREHOUSE_LIMITS } from '../types';
import type { Warehouse } from '../types';

interface CreateWarehouseProps {
  onClose: () => void;
  onSuccess: () => void;
}

const CreateWarehouse: React.FC<CreateWarehouseProps> = ({ onClose, onSuccess }) => {
  const { createWarehouse, isLoading } = useWarehouseStore();
  
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    areasCount: 1,
    unitsPerArea: 1,
    shelvesPerUnit: 1,
    boxesPerShelf: 1,
    existingBoxes: 0,
    city: '',
    governorate: '',
    address: '',
    phones: [''],
    emails: [''],
  });
  
  const [errors, setErrors] = useState<string[]>([]);
  const [previewWarehouse, setPreviewWarehouse] = useState<Warehouse | null>(null);

  // Generate preview when form data changes
  useEffect(() => {
    const preview: Warehouse = {
      id: 'preview',
      ...formData,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    setPreviewWarehouse(preview);
  }, [formData]);

  // Validate form data
  useEffect(() => {
    const validateForm = async () => {
      const validationErrors = await validateWarehouseCounts(formData);
      setErrors(validationErrors);
    };
    validateForm();
  }, [formData]);

  const handleInputChange = (field: keyof typeof formData, value: string | number) => {
    setFormData(prev => ({
      ...prev,
      [field]: typeof value === 'string' ? value : Math.max(1, value),
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (errors.length > 0) return;

    try {
      await createWarehouse(formData);
      onSuccess();
    } catch (error) {
      console.error('Failed to create warehouse:', error);
    }
  };

  const cap = previewWarehouse ? capacity(previewWarehouse) : null;
  const layout = previewWarehouse ? generateLayout(previewWarehouse) : null;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button
          variant="plain"
          onClick={onClose}
          className="flex items-center gap-2"
        >
          <TbArrowLeft size={20} />
          Back to Warehouses
        </Button>
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
            Create New Warehouse
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Define the structure and capacity of your new warehouse
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Form */}
        <Card className="p-6">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-6">
            Warehouse Details
          </h2>
          
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Basic Info */}
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Warehouse Name *
                </label>
                <Input
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  placeholder="Enter warehouse name"
                  required
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Description
                </label>
                <Input
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  placeholder="Optional description"
                />
              </div>
            </div>

            {/* Location Information */}
            <div>
              <h3 className="text-md font-medium text-gray-900 dark:text-gray-100 mb-4">
                Location Information
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    City
                  </label>
                  <Input
                    value={formData.city}
                    onChange={(e) => handleInputChange('city', e.target.value)}
                    placeholder="Enter city"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Governorate
                  </label>
                  <Input
                    value={formData.governorate}
                    onChange={(e) => handleInputChange('governorate', e.target.value)}
                    placeholder="Enter governorate"
                  />
                </div>
              </div>
              
              <div className="mt-4">
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Address
                </label>
                <Input
                  value={formData.address}
                  onChange={(e) => handleInputChange('address', e.target.value)}
                  placeholder="Enter full address"
                />
              </div>
            </div>

            {/* Contact Information */}
            <div>
              <h3 className="text-md font-medium text-gray-900 dark:text-gray-100 mb-4">
                Contact Information
              </h3>
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Phone Numbers
                  </label>
                  {formData.phones.map((phone, index) => (
                    <div key={index} className="flex gap-2 mb-2">
                      <Input
                        value={phone}
                        onChange={(e) => {
                          const newPhones = [...formData.phones];
                          newPhones[index] = e.target.value;
                          setFormData(prev => ({ ...prev, phones: newPhones }));
                        }}
                        placeholder="Enter phone number"
                      />
                      {formData.phones.length > 1 && (
                        <Button
                          type="button"
                          variant="plain"
                          onClick={() => {
                            const newPhones = formData.phones.filter((_, i) => i !== index);
                            setFormData(prev => ({ ...prev, phones: newPhones }));
                          }}
                          className="text-red-500 hover:text-red-700"
                        >
                          Remove
                        </Button>
                      )}
                    </div>
                  ))}
                  <Button
                    type="button"
                    variant="plain"
                    onClick={() => setFormData(prev => ({ ...prev, phones: [...prev.phones, ''] }))}
                    className="text-blue-600 hover:text-blue-800"
                  >
                    + Add Phone
                  </Button>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Email Addresses
                  </label>
                  {formData.emails.map((email, index) => (
                    <div key={index} className="flex gap-2 mb-2">
                      <Input
                        value={email}
                        onChange={(e) => {
                          const newEmails = [...formData.emails];
                          newEmails[index] = e.target.value;
                          setFormData(prev => ({ ...prev, emails: newEmails }));
                        }}
                        placeholder="Enter email address"
                        type="email"
                      />
                      {formData.emails.length > 1 && (
                        <Button
                          type="button"
                          variant="plain"
                          onClick={() => {
                            const newEmails = formData.emails.filter((_, i) => i !== index);
                            setFormData(prev => ({ ...prev, emails: newEmails }));
                          }}
                          className="text-red-500 hover:text-red-700"
                        >
                          Remove
                        </Button>
                      )}
                    </div>
                  ))}
                  <Button
                    type="button"
                    variant="plain"
                    onClick={() => setFormData(prev => ({ ...prev, emails: [...prev.emails, ''] }))}
                    className="text-blue-600 hover:text-blue-800"
                  >
                    + Add Email
                  </Button>
                </div>
              </div>
            </div>

            {/* Structure Configuration */}
            <div>
              <h3 className="text-md font-medium text-gray-900 dark:text-gray-100 mb-4">
                Storage Structure
              </h3>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Areas Count
                  </label>
                  <Input
                    type="number"
                    min={WAREHOUSE_LIMITS.MIN_AREAS}
                    max={WAREHOUSE_LIMITS.MAX_AREAS}
                    value={formData.areasCount}
                    onChange={(e) => handleInputChange('areasCount', parseInt(e.target.value))}
                    required
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Min: {WAREHOUSE_LIMITS.MIN_AREAS}, Max: {WAREHOUSE_LIMITS.MAX_AREAS}
                  </p>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Units per Area
                  </label>
                  <Input
                    type="number"
                    min={WAREHOUSE_LIMITS.MIN_UNITS_PER_AREA}
                    max={WAREHOUSE_LIMITS.MAX_UNITS_PER_AREA}
                    value={formData.unitsPerArea}
                    onChange={(e) => handleInputChange('unitsPerArea', parseInt(e.target.value))}
                    required
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Min: {WAREHOUSE_LIMITS.MIN_UNITS_PER_AREA}, Max: {WAREHOUSE_LIMITS.MAX_UNITS_PER_AREA}
                  </p>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Shelves per Unit
                  </label>
                  <Input
                    type="number"
                    min={WAREHOUSE_LIMITS.MIN_SHELVES_PER_UNIT}
                    max={WAREHOUSE_LIMITS.MAX_SHELVES_PER_UNIT}
                    value={formData.shelvesPerUnit}
                    onChange={(e) => handleInputChange('shelvesPerUnit', parseInt(e.target.value))}
                    required
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Min: {WAREHOUSE_LIMITS.MIN_SHELVES_PER_UNIT}, Max: {WAREHOUSE_LIMITS.MAX_SHELVES_PER_UNIT}
                  </p>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Boxes per Shelf
                  </label>
                  <Input
                    type="number"
                    min={WAREHOUSE_LIMITS.MIN_BOXES_PER_SHELF}
                    max={WAREHOUSE_LIMITS.MAX_BOXES_PER_SHELF}
                    value={formData.boxesPerShelf}
                    onChange={(e) => handleInputChange('boxesPerShelf', parseInt(e.target.value))}
                    required
                  />
                  <p className="text-xs text-gray-500 mt-1">
                    Min: {WAREHOUSE_LIMITS.MIN_BOXES_PER_SHELF}, Max: {WAREHOUSE_LIMITS.MAX_BOXES_PER_SHELF}
                  </p>
                </div>
              </div>
            </div>

            {/* Existing Boxes */}
            <div>
              <h3 className="text-md font-medium text-gray-900 dark:text-gray-100 mb-4">
                Current Inventory
              </h3>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Existing Boxes
                </label>
                <Input
                  type="number"
                  min={0}
                  value={formData.existingBoxes}
                  onChange={(e) => handleInputChange('existingBoxes', parseInt(e.target.value) || 0)}
                  placeholder="Number of existing boxes"
                />
                <p className="text-xs text-gray-500 mt-1">
                  Number of boxes currently stored in the warehouse
                </p>
              </div>
            </div>

            {/* Validation Errors */}
            {errors.length > 0 && (
              <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
                <div className="flex items-center gap-2 text-red-700 dark:text-red-400 mb-2">
                  <TbX size={20} />
                  <span className="font-medium">Validation Errors</span>
                </div>
                <ul className="text-sm text-red-600 dark:text-red-300 space-y-1">
                  {errors.map((error, index) => (
                    <li key={index}>• {error}</li>
                  ))}
                </ul>
              </div>
            )}

            {/* Submit Button */}
            <Button
              type="submit"
              variant="solid"
              disabled={isLoading || errors.length > 0 || !formData.name}
              className="w-full flex items-center justify-center gap-2"
            >
              {isLoading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                  Creating...
                </>
              ) : (
                <>
                  <TbCheck size={20} />
                  Create Warehouse
                </>
              )}
            </Button>
          </form>
        </Card>

        {/* Preview */}
        <div className="space-y-6">
          {/* Capacity Summary */}
          <Card className="p-6">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
              Capacity Preview
            </h2>
            {cap && (
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                  <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                    {formatNumber(cap.totalBoxes)}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">Total Boxes</div>
                </div>
                <div className="text-center p-4 bg-indigo-50 dark:bg-indigo-900/20 rounded-lg">
                  <div className="text-2xl font-bold text-indigo-600 dark:text-indigo-400">
                    {formatNumber(cap.totalShelves)}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">Total Shelves</div>
                </div>
                <div className="text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                  <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">
                    {formatNumber(cap.totalUnits)}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">Total Units</div>
                </div>
                <div className="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                  <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                    {formatNumber(cap.totalAreas)}
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">Total Areas</div>
                </div>
              </div>
            )}
          </Card>

          {/* Layout Preview */}
          <Card className="p-6">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
              Layout Preview
            </h2>
            {layout && (
              <div className="space-y-4">
                {layout.areas.slice(0, 3).map((area, areaIdx) => (
                  <div key={area.id} className="border border-gray-200 dark:border-gray-700 rounded-lg p-3">
                    <div className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-2">
                      {area.id}
                    </div>
                    <div className="grid grid-cols-4 gap-2">
                      {area.units.slice(0, 4).map((unit, unitIdx) => (
                        <div key={unit.id} className="text-center p-2 bg-gray-50 dark:bg-gray-800 rounded text-xs">
                          <div className="font-medium">{unit.id}</div>
                          <div className="text-gray-500">{unit.shelves.length} shelves</div>
                        </div>
                      ))}
                      {area.units.length > 4 && (
                        <div className="text-center p-2 bg-gray-100 dark:bg-gray-700 rounded text-xs text-gray-500">
                          +{area.units.length - 4} more
                        </div>
                      )}
                    </div>
                  </div>
                ))}
                {layout.areas.length > 3 && (
                  <div className="text-center text-sm text-gray-500">
                    +{layout.areas.length - 3} more areas
                  </div>
                )}
              </div>
            )}
          </Card>
        </div>
      </div>
    </div>
  );
};

export default CreateWarehouse;
