import { useMemo, useState, useEffect } from 'react'
import Tooltip from '@/components/ui/Tooltip'
import DataTable from '@/components/shared/DataTable'
import ConfirmDialog from '@/components/shared/ConfirmDialog'
import { useStock } from '@/utils/hooks/useStock'
import { TbPencil, TbTrash } from 'react-icons/tb'
import type { OnSortParam, ColumnDef } from '@/components/shared/DataTable'
import type { StockColumns } from '@/@types/stocks'
import { Tag } from '@/components/ui'
import useTranslation from '@/utils/hooks/useTranslation'

// Enhanced Stock interface with display fields
export interface StockWithDisplay extends StockColumns {
    cityName: string
    governorateName: string
    countryName: string
    displayCity: string
    displayGovernorate: string
    displayCountry: string
}

interface StockListTableProps {
    onEdit: (stock_id: number) => void
}

const ActionColumn = ({
    onEdit,
    onDelete,
}: {
    onEdit: () => void
    onDelete: () => void
}) => {
    const { t } = useTranslation()
    return (
        <div className="flex items-center justify-center gap-3">
            <Tooltip title={t('nav.shared.edit')}>
                <div
                    className={`text-xl cursor-pointer select-none font-semibold`}
                    role="button"
                    onClick={onEdit}
                >
                    <TbPencil />
                </div>
            </Tooltip>
            <Tooltip title={t('nav.shared.delete')}>
                <div
                    className={`text-xl cursor-pointer select-none font-semibold`}
                    role="button"
                    onClick={onDelete}
                >
                    <TbTrash />
                </div>
            </Tooltip>
        </div>
    )
}

const statusColor: Record<string, string> = {
    معتمد: 'bg-emerald-200 dark:bg-emerald-200 text-gray-900 dark:text-gray-900',
    'غير معتمد': 'bg-red-200 dark:bg-red-200 text-gray-900 dark:text-gray-900',
}

const StockListTable = ({ onEdit }: StockListTableProps) => {
    const { t } = useTranslation()
    const [deleteConfirmationOpen, setDeleteConfirmationOpen] = useState(false)
    const [toDeleteId, setToDeleteId] = useState<number | null>(null)

    const {
        // processedStocks,
        paginatedStocks,
        loading,
        // pagination,
        // sortConfig,
        handleSort,
        goToPage,
        changePageSize,
        deleteStock,
        fetchAllStocks,
        selectedStocks,
        handleSelect,
        handleSelectAll,
    } = useStock()

    const handleCancel = () => {
        setDeleteConfirmationOpen(false)
        setToDeleteId(null)
    }

    const handleDelete = (stock: StockColumns) => {
        setDeleteConfirmationOpen(true)
        setToDeleteId(stock.id)
    }

    const handleEdit = (stock: StockColumns) => {
        onEdit(stock.id)
    }

    const handleConfirmDelete = async () => {
        if (toDeleteId) {
            try {
                await deleteStock(toDeleteId)
                setDeleteConfirmationOpen(false)
                setToDeleteId(null)
                // Refresh the data after successful delete
                fetchAllStocks()
            } catch (error) {
                console.error('Failed to delete stock:', error)
            }
        }
    }

    // Fetch stocks on component mount
    useEffect(() => {
        fetchAllStocks()
    }, [fetchAllStocks])

    const allColumns: ColumnDef<StockWithDisplay>[] = useMemo(
        () => [
            // {
            //     header: t('nav.shared.id'),
            //     accessorKey: 'id',
            //     cell: (props) => {
            //         const row = props.row.original
            //         return <span className="heading-text">{row.id}</span>
            //     },
            // },
            {
                header: t('nav.shared.name'),
                accessorKey: 'name',
                cell: (props) => {
                    const row = props.row.original
                    return <span className="heading-text">{row.name}</span>
                },
            },
            {
                header: t('nav.buildings.village'),
                accessorKey: 'villageNameAr',
                cell: (props) => {
                    const row = props.row.original
                    return <span className="heading-text">{row.villageNameAr}</span>
                },
            },
            {
                header: t('nav.buildings.city'),
                accessorKey: 'cityOrDistrictNameAr',
                cell: (props) => {
                    const row = props.row.original
                    return <span className="heading-text">{row.cityOrDistrictNameAr}</span>
                },
            },
            {
                header: t('nav.buildings.governorate'),
                accessorKey: 'governorateNameAr',
                cell: (props) => {
                    const row = props.row.original
                    return (
                        <span className="heading-text">{row.governorateNameAr}</span>
                    )
                },
            },
            // {
            //     header: t('nav.buildings.country'),
            //     accessorKey: 'countryNameAr',
            //     cell: (props) => {
            //         const row = props.row.original
            //         return (
            //             <span className="heading-text">{row.countryNameAr}</span>
            //         )
            //     },
            // },
            {
                header: t('nav.shared.manager'),
                accessorKey: 'managerName',
                cell: (props) => {
                    const row = props.row.original
                    return <span className="heading-text">{row.managerName}</span>
                },
            },
            {
                header: t('nav.shared.status'),
                accessorKey: 'status',
                cell: (props) => {
                    const row = props.row.original
                    const statusText = row.status === 1 ? 'معتمد' : 'غير معتمد'
                    return (
                        <div className="flex items-center justify-center">
                            <Tag className={statusColor[statusText]}>
                                <span className="capitalize">{statusText}</span>
                            </Tag>
                        </div>
                    )
                },
            },
            {
                header: t('nav.shared.edit'),
                id: 'action',
                cell: (props) => (
                    <ActionColumn
                        onEdit={() => handleEdit(props.row.original as unknown as StockColumns)}
                        onDelete={() => handleDelete(props.row.original as unknown as StockColumns)}
                    />
                ),
            },
        ],
        [t],
    )

    const columns: ColumnDef<StockWithDisplay>[] = useMemo(() => {
        return allColumns
    }, [allColumns])

    const handlePaginationChange = (page: number) => {
        goToPage(page)
    }

    const handleSelectChange = (value: number) => {
        changePageSize(value)
    }

    const handleSortChange = (sort: OnSortParam) => {
        handleSort(sort.key as keyof StockColumns)
    }

    const handleRowSelect = (checked: boolean, row: StockWithDisplay) => {
        handleSelect(row, checked)
    }

    const handleAllRowSelect = (checked: boolean, rows: { original: StockWithDisplay }[]) => {
        if (checked) {
            const originalRows = rows.map((row) => row.original)
            handleSelectAll(true, originalRows)
        } else {
            handleSelectAll(false, [])
        }
    }

    // // Transform paginated stocks to include display fields
    // const stocksWithDisplay: StockWithDisplay[] = useMemo(() => {
    //     return paginatedStocks.data.map(stock => ({
    //         ...stock,
    //         cityName: stock.cityOrDistrictNameAr,
    //         governorateName: stock.governorateNameAr,
    //         countryName: stock.countryNameAr,
    //         displayCity: stock.cityOrDistrictNameAr,
    //         displayGovernorate: stock.governorateNameAr,
    //         displayCountry: stock.countryNameAr,
    //     }))
    // }, [paginatedStocks.data])

    return (
        <>
            <DataTable
                selectable
                columns={columns}
                data={paginatedStocks.data}
                noData={!loading && paginatedStocks.data.length === 0}
                skeletonAvatarColumns={[0]}
                skeletonAvatarProps={{ width: 28, height: 28 }}
                loading={loading}
                pagingData={{
                    total: paginatedStocks.total,
                    pageIndex: paginatedStocks.page,
                    pageSize: paginatedStocks.pageSize,
                }}
                checkboxChecked={(row) =>
                    selectedStocks.some((selected) => selected.id === row.id)
                }
                cellBorder={true}
                onPaginationChange={handlePaginationChange}
                onSelectChange={handleSelectChange}
                onSort={handleSortChange}
                onCheckBoxChange={handleRowSelect}
                onIndeterminateCheckBoxChange={handleAllRowSelect}
            />

            <ConfirmDialog
                isOpen={deleteConfirmationOpen}
                type="danger"
                title={t('nav.shared.deleteStock')}
                onClose={handleCancel}
                onRequestClose={handleCancel}
                onCancel={handleCancel}
                onConfirm={handleConfirmDelete}
            >
                <p>{t('nav.shared.confirmDeleteStock')}</p>
            </ConfirmDialog>
        </>
    )
}

export default StockListTable
