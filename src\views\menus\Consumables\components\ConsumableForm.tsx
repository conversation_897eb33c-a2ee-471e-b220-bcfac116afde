import { useEffect } from 'react'
import { Form } from '@/components/ui/Form'
import Container from '@/components/shared/Container'
import BottomStickyBar from '@/components/template/BottomStickyBar'
import BasicInfoSection from './BasicInfoSection'
import { useForm } from 'react-hook-form'
import { z } from 'zod'
import { zodResolver } from '@hookform/resolvers/zod'
import isEmpty from 'lodash/isEmpty'
import type { Consumables } from '../types'
import type { ZodType } from 'zod'
import type { CommonProps } from '@/@types/common'

type ConsumableFormProps = {
    onFormSubmit: (values: Consumables) => void
    defaultValues?: Consumables
    newConsumable?: boolean
} & CommonProps

const countRangeSchema = z.object({
    from: z.union([z.string().min(1), z.number().min(1)]),
    to: z.union([z.string().min(1), z.number().min(1)]),
})

const validationSchema: ZodType<Consumables> = z.object({
    id: z.string().optional(),
    category: z.string().min(1, { message: 'Category is required!' }),
    count: z.union([z.string(), z.number(), countRangeSchema], {
        errorMap: () => ({ message: 'Count is required!' }),
    }),
    description: z.string().min(1, { message: 'Description is required!' }),
    wareHouse: z.string().min(1, { message: 'Warehouse is required!' }),
    status: z.string().min(1, { message: 'Status is required!' }),
    attachments: z.string().min(1, { message: 'Attachments are required!' }),
    assignedBy: z.string().optional(),
    createdAt: z.string().optional(),
})

const ConsumableForm = (props: ConsumableFormProps) => {
    const { onFormSubmit, defaultValues = {}, children } = props

    const {
        handleSubmit,
        reset,
        formState: { errors },
        control,
    } = useForm<Consumables>({
        defaultValues: {
            ...defaultValues,
        },
        resolver: zodResolver(validationSchema),
    })

    useEffect(() => {
        if (!isEmpty(defaultValues)) {
            reset(defaultValues)
        }
        // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [JSON.stringify(defaultValues)])

    const onSubmit = (values: Consumables) => {
        onFormSubmit?.(values)
    }

    return (
        <Form
            className="flex w-full h-full gap-4"
            containerClassName="flex flex-col w-full justify-between gap-4"
            onSubmit={handleSubmit(onSubmit)}
        >
            <Container>
                <div className="flex flex-col sm:flex-row justify-between gap-4">
                    {/* <CategorySelection control={control} errors={errors} /> */}
                    <BasicInfoSection control={control} errors={errors} />
                </div>
            </Container>
            <BottomStickyBar>{children}</BottomStickyBar>
        </Form>
    )
}

export default ConsumableForm
