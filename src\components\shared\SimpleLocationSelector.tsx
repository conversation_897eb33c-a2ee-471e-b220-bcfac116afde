/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useEffect, useState } from 'react'
import { useDispatch, useSelector } from 'react-redux'
import { Select } from '@/components/ui'
import { FormItem } from '@/components/ui/Form'
import { Controller } from 'react-hook-form'
import { useTranslation } from 'react-i18next'
import {
    fetchCountries,
    fetchGovernorates,
    fetchCities,
    fetchDistricts,
} from '@/store/slices/locationSlice'
import {
    selectCountries,
    selectGovernorates,
    selectCities,
    selectDistricts,
    selectLoadingCountries,
    selectLoadingGovernorates,
    selectLoadingCities,
    selectLoadingDistricts,
    locationToSelectOptions,
} from '@/store/selectors/locationSelector'
import type { AppDispatch } from '@/store/store'

export interface SimpleLocationSelectorProps {
    control: any
    errors: any
    className?: string
    gridCols?: string
}

const SimpleLocationSelector: React.FC<SimpleLocationSelectorProps> = ({
    control,
    errors,
    className = '',
    gridCols = 'grid-cols-1 md:grid-cols-2',
}) => {
    const { t } = useTranslation()
    const dispatch = useDispatch<AppDispatch>()

    // Redux state using selectors
    const countries = useSelector(selectCountries)
    const governorates = useSelector(selectGovernorates)
    const cities = useSelector(selectCities)
    const districts = useSelector(selectDistricts)

    const isLoadingCountries = useSelector(selectLoadingCountries)
    const isLoadingGovernorates = useSelector(selectLoadingGovernorates)
    const isLoadingCities = useSelector(selectLoadingCities)
    const isLoadingDistricts = useSelector(selectLoadingDistricts)

    // Local state for tracking selections
    const [selectedCountry, setSelectedCountry] = useState<string>('')
    const [selectedGovernorate, setSelectedGovernorate] = useState<string>('')
    const [selectedCity, setSelectedCity] = useState<string>('')

    // Load countries on mount
    useEffect(() => {
        if (countries.length === 0) {
            dispatch(fetchCountries())
        }
    }, [dispatch, countries.length])

    // Convert data to select options
    const countryOptions = locationToSelectOptions(countries)
    const governorateOptions = locationToSelectOptions(governorates)
    const cityOptions = locationToSelectOptions(cities)
    const districtOptions = locationToSelectOptions(districts)

    // Handle cascading changes
    const handleCountryChange = async (
        countryCode: string,
        onChange: (value: string) => void,
    ) => {
        setSelectedCountry(countryCode)
        setSelectedGovernorate('')
        setSelectedCity('')
        onChange(countryCode)

        if (countryCode) {
            await dispatch(fetchGovernorates(countryCode))
        }
    }

    const handleGovernorateChange = async (
        governorateCode: string,
        onChange: (value: string) => void,
    ) => {
        setSelectedGovernorate(governorateCode)
        setSelectedCity('')
        onChange(governorateCode)

        if (governorateCode) {
            await dispatch(fetchCities(governorateCode))
        }
    }

    const handleCityChange = async (
        cityCode: string,
        onChange: (value: string) => void,
    ) => {
        setSelectedCity(cityCode)
        onChange(cityCode)

        if (cityCode) {
            await dispatch(fetchDistricts(cityCode))
        }
    }

    return (
        <div className={`grid ${gridCols} gap-4 ${className}`}>
            {/* Country */}
            <FormItem
                className="mb-4"
                label={t('nav.buildings.country')}
                invalid={Boolean(errors.countryNameAr)}
                errorMessage={errors.countryNameAr?.message}
            >
                <Controller
                    name="countryNameAr"
                    control={control}
                    render={({ field }) => (
                        <Select
                            options={countryOptions}
                            placeholder={t('nav.buildings.selectCountry')}
                            isLoading={isLoadingCountries}
                            value={countryOptions.filter(
                                (option) => option.value === field.value,
                            )}
                            onChange={(option) =>
                                handleCountryChange(
                                    option?.value || '',
                                    field.onChange,
                                )
                            }
                        />
                    )}
                />
            </FormItem>

            {/* Governorate */}
            <FormItem
                className="mb-4"
                label={t('nav.buildings.governorate')}
                invalid={Boolean(errors.governorateNameAr)}
                errorMessage={errors.governorateNameAr?.message}
            >
                <Controller
                    name="governorateNameAr"
                    control={control}
                    render={({ field }) => (
                        <Select
                            options={governorateOptions}
                            placeholder={t('nav.buildings.selectGovernorate')}
                            isLoading={isLoadingGovernorates}
                            isDisabled={
                                !selectedCountry ||
                                governorateOptions.length === 0
                            }
                            value={governorateOptions.filter(
                                (option) => option.value === field.value,
                            )}
                            onChange={(option) =>
                                handleGovernorateChange(
                                    option?.value || '',
                                    field.onChange,
                                )
                            }
                        />
                    )}
                />
            </FormItem>

            {/* City */}
            <FormItem
                className="mb-4"
                label={t('nav.buildings.city')}
                invalid={Boolean(errors.cityOrDistrictNameAr)}
                errorMessage={errors.cityOrDistrictNameAr?.message}
            >
                <Controller
                    name="cityOrDistrictNameAr"
                    control={control}
                    render={({ field }) => (
                        <Select
                            options={cityOptions}
                            placeholder={t('nav.buildings.selectCity')}
                            isLoading={isLoadingCities}
                            isDisabled={
                                !selectedGovernorate || cityOptions.length === 0
                            }
                            value={cityOptions.filter(
                                (option) => option.value === field.value,
                            )}
                            onChange={(option) =>
                                handleCityChange(
                                    option?.value || '',
                                    field.onChange,
                                )
                            }
                        />
                    )}
                />
            </FormItem>

            {/* Village/District */}
            <FormItem
                className="mb-4"
                label={t('nav.buildings.village')}
                invalid={Boolean(errors.villageNameAr)}
                errorMessage={errors.villageNameAr?.message}
            >
                <Controller
                    name="villageNameAr"
                    control={control}
                    render={({ field }) => (
                        <Select
                            options={districtOptions}
                            placeholder={t('nav.buildings.selectVillage')}
                            isLoading={isLoadingDistricts}
                            isDisabled={
                                !selectedCity || districtOptions.length === 0
                            }
                            value={districtOptions.filter(
                                (option) => option.value === field.value,
                            )}
                            onChange={(option) =>
                                field.onChange(option?.value || '')
                            }
                        />
                    )}
                />
            </FormItem>
        </div>
    )
}

export default SimpleLocationSelector

/**
 * Usage Example:
 *
 * import SimpleLocationSelector from '@/components/shared/SimpleLocationSelector'
 *
 * <SimpleLocationSelector
 *     control={control}
 *     errors={errors}
 *     gridCols="grid-cols-1 md:grid-cols-2"
 *     className="mb-4"
 * />
 */
